# Environment: local, beta, production
VITE_APP_ENV=local

# Backend API base URL
VITE_APP_API_URL=http://localhost:8080

# Port your frontend runs on
VITE_APP_PORT=3000

# Backend type: spring / nest / django
VITE_APP_BACKEND_TYPE=spring

# Google OAuth2 Authorization URL
# Update with actual client-side redirect URI if needed
VITE_APP_GOOGLE_OAUTH_URL=http://localhost:8080/oauth2/authorize/google?redirect_uri=http://localhost:3000/oauth2/redirect
