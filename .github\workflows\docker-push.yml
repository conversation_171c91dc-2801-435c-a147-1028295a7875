name: Docker Build and Push to DOCR

on:
  workflow_call:
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN:
        required: true

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: [self-hosted, Linux]
    if: github.event_name == 'push' && (github.ref_name == 'main' || startsWith(github.ref_name, 'deployment'))
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
      - name: Set environment (default to kubernetes.spring for deployment)
        run: |
          echo "ENVIRONMENT=${ENVIRONMENT:-kubernetes.spring}" >> $GITHUB_ENV
      - name: Set environment file based on deployment target
        run: |
          if [[ "${{ github.ref_name }}" == "main" ]]; then
            echo "ENV_FILE=.env.kubernetes.spring" >> $GITHUB_ENV
          elif [[ "${{ github.ref_name }}" == "deployment"* ]]; then
            echo "ENV_FILE=.env.kubernetes.spring" >> $GITHUB_ENV
          else
            echo "ENV_FILE=.env.spring" >> $GITHUB_ENV
          fi
      - name: Authenticate Docker with DigitalOcean Container Registry (DOCR)
        run: doctl registry login
      - name: Set build script based on environment
        run: |
          if [[ "${{ env.ENV_FILE }}" == ".env.kubernetes.spring" ]]; then
            echo "BUILD_SCRIPT=build:kubernetes.spring" >> $GITHUB_ENV
          elif [[ "${{ env.ENV_FILE }}" == ".env.kubernetes.nest" ]]; then
            echo "BUILD_SCRIPT=build:kubernetes.nest" >> $GITHUB_ENV
          elif [[ "${{ env.ENV_FILE }}" == ".env.kubernetes.django" ]]; then
            echo "BUILD_SCRIPT=build:kubernetes.django" >> $GITHUB_ENV
          else
            echo "BUILD_SCRIPT=build:spring" >> $GITHUB_ENV
          fi
      - name: Build Docker Image
        run: docker build --build-arg ENV_FILE=${{ env.ENV_FILE }} --build-arg BUILD_SCRIPT=${{ env.BUILD_SCRIPT }} -t ai-react-frontend .
      - name: Tag Docker Image for DOCR
        run: |
          IMAGE_LATEST="registry.digitalocean.com/doks-registry/ai-react-frontend:latest"
          docker tag ai-react-frontend $IMAGE_LATEST
          echo "Tagged image: $IMAGE_LATEST"
      - name: Push Image to DOCR
        run: |
          docker push registry.digitalocean.com/doks-registry/ai-react-frontend:latest
          echo "Pushed latest image to registry"