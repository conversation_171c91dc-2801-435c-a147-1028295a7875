name: Docker Build and Push to DOCR

on:
  workflow_call:
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN:
        required: true

jobs:
  build-and-push:
    name: <PERSON><PERSON> and Push Docker Image
    runs-on: [self-hosted, Linux]
    if: github.event_name == 'push' && (github.ref_name == 'main' || startsWith(github.ref_name, 'deployment'))
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
      - name: Set environment (default to spring)
        run: |
          echo "ENVIRONMENT=${ENVIRONMENT:-spring}" >> $GITHUB_ENV
      - name: Authenticate Docker with DigitalOcean Container Registry (DOCR)
        run: doctl registry login
      - name: Build Docker Image
        run: docker build --build-arg ENV_FILE=${{ env.ENV_FILE }} -t ai-react-frontend .
        env:
          ENV_FILE: .env.spring
      - name: Tag Docker Image for DOCR
        run: |
          IMAGE_LATEST="registry.digitalocean.com/doks-registry/ai-react-frontend:latest"
          docker tag ai-react-frontend $IMAGE_LATEST
          echo "Tagged image: $IMAGE_LATEST"
      - name: Push Image to DOCR
        run: |
          docker push registry.digitalocean.com/doks-registry/ai-react-frontend:latest
          echo "Pushed latest image to registry"