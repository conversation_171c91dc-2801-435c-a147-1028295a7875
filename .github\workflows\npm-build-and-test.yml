name: Run Tests and Build (npm)

on:
  workflow_call:

jobs:
  test-and-build:
    runs-on: [self-hosted, Linux]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Install dependencies
        run: npm ci
      - name: Run Tests (with coverage)
        run: npm run test:coverage
      - name: Build (Spring)
        run: npm run build:spring
      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: dist/ 