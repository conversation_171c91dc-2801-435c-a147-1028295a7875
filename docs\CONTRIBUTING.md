# Contributing to AI React Frontend – <PERSON>gin Module

Thank you for your interest in contributing! Your help is welcome and appreciated.

## How to Contribute

1. **Fork the repository** and create your branch from `main`.
2. **Clone your fork** and set up the project locally.
3. **Create a descriptive branch name** (e.g., `feature/login-form`, `bugfix/fix-auth-error`).
4. **Make your changes** and add tests as appropriate.
5. **Run the test suite** to ensure nothing is broken.
6. **Commit your changes** with clear, descriptive messages.
7. **Push to your fork** and submit a pull request (PR) to the main repository.

## Submitting Issues

- Please use the [GitHub Issues](../../issues) tab to report bugs or request features.
- Provide as much detail as possible, including steps to reproduce, expected behavior, and screenshots if relevant.

## Coding Standards

- Use consistent code style (see `.eslintrc` and `.prettierrc` if available).
- Write clear, maintainable code and add comments where necessary.
- Add or update tests for any new features or bug fixes.

## Code Review Process

- All PRs will be reviewed by a maintainer.
- Please respond to feedback and make any requested changes.
- PRs must pass all CI checks before merging.

Thank you for helping make this project better! 