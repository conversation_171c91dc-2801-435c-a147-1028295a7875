# Docker Hub Commands: Pushing Your Images

This guide explains how to tag and push your Docker images to Docker Hub.

---

## 1. Prerequisites
- You have a [Docker Hub](https://hub.docker.com/) account
- You are logged in to Docker Hub from your terminal

```
docker login
```

---

## 2. Tag Your Image for Docker Hub

Format:
```
docker tag <local-image>:<tag> <dockerhub-username>/<repository>:<tag>
```

**Example:**
```
docker tag ai-react-frontend-spring shashank1088/ai-react-frontend-spring:latest
```
- Replace `shashank1088` with your Docker Hub username if different
- Repeat for other images (nest, django)

---

## 3. Push the Image to Docker Hub

Format:
```
docker push <dockerhub-username>/<repository>:<tag>
```

**Example:**
```
docker push shashank1088/ai-react-frontend-spring:latest
```
- Repeat for other images (nest, django)

---

## 4. Verify on Docker Hub
- Go to https://hub.docker.com/repositories
- Check that your images appear

---

## 5. Full Example for All Environments

```
# Spring

docker tag ai-react-frontend-spring shashank1088/ai-react-frontend-spring:latest
docker push shashank1088/ai-react-frontend-spring:latest

# Nest

docker tag ai-react-frontend-nest shashank1088/ai-react-frontend-nest:latest
docker push shashank1088/ai-react-frontend-nest:latest

# Django

docker tag ai-react-frontend-django shashank1088/ai-react-frontend-django:latest
docker push shashank1088/ai-react-frontend-django:latest
```

---

## 6. Notes
- You can use any tag (e.g., `:v1`, `:prod`), but `:latest` is common.
- Replace `shashank1088` with your actual Docker Hub username if different.
- Make sure your images are built before tagging and pushing. 


----------------------------------------
This guide explains how to run your Docker image from Docker Hub on any other machine.

---

## 1. Prerequisites
- Docker must be installed on the other laptop
- The other laptop must have internet access

---

## 2. Pull the Image from Docker Hub

On the other laptop, open a terminal and run:

```
docker pull shashank1088/ai-react-frontend-spring:latest
```

- Replace `ai-react-frontend-spring` with `ai-react-frontend-nest` or `ai-react-frontend-django` as needed.

---

## 2. After Pulling Run the container

On the other laptop, open a terminal and run:

```
docker run -d -p 3000:80 --name ai-react-frontend-spring shashank1088/ai-react-frontend-spring:latest
```