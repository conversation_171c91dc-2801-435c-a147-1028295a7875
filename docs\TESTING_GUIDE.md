# Testing Guide

This project uses <PERSON><PERSON> as the testing framework along with React Testing Library for component testing.

## Setup

Jest has been configured with the following features:
- **Test Environment**: jsdom (for DOM testing)
- **TypeScript Support**: Full TypeScript and JSX support
- **Path Aliases**: Support for `@/` imports
- **Static Asset Mocking**: CSS, images, and other assets are mocked
- **Coverage Reports**: Code coverage tracking with thresholds

## Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode (re-runs on file changes)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### Test File Patterns

Jest will automatically find and run test files that match these patterns:
- `src/**/__tests__/**/*.(js|jsx|ts|tsx)`
- `src/**/?(*.)(test|spec).(js|jsx|ts|tsx)`

## Writing Tests

### Component Testing

Example test for a React component:

```jsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from '../button';

describe('Button Component', () => {
  it('renders button with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Utility Function Testing

Example test for utility functions:

```typescript
import { cn } from '../utils';

describe('cn utility function', () => {
  it('combines class names correctly', () => {
    const result = cn('class1', 'class2');
    expect(result).toBe('class1 class2');
  });

  it('handles conditional classes', () => {
    const result = cn('base-class', true && 'conditional-class');
    expect(result).toBe('base-class conditional-class');
  });
});
```

### Mocking

#### Mocking Modules

```javascript
// Mock a module
jest.mock('../contexts/AuthContext');

// Mock with custom implementation
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  }
}));
```

#### Mocking Components

```javascript
// Mock a component
jest.mock('../GoogleLoginButton', () => {
  return function MockGoogleLoginButton() {
    return <div data-testid="google-login-button">Google Login</div>;
  };
});
```

## Testing Best Practices

### 1. Test Structure
- Use `describe` blocks to group related tests
- Use descriptive test names that explain what is being tested
- Follow the Arrange-Act-Assert pattern

### 2. User-Centric Testing
- Test from the user's perspective
- Use `screen.getByRole()`, `screen.getByLabelText()`, etc.
- Avoid testing implementation details

### 3. Async Testing
- Use `waitFor()` for async operations
- Use `userEvent` for user interactions
- Always await async operations

### 4. Mocking Guidelines
- Mock external dependencies
- Mock complex components when testing parent components
- Keep mocks simple and focused

## Available Testing Utilities

### React Testing Library
- `render()` - Render components
- `screen` - Query rendered elements
- `fireEvent` - Trigger events (prefer userEvent)
- `waitFor()` - Wait for async operations

### User Event
- `userEvent.click()` - Simulate clicks
- `userEvent.type()` - Simulate typing
- `userEvent.selectOptions()` - Select from dropdowns

### Jest Matchers
- `toBeInTheDocument()` - Element exists in DOM
- `toHaveClass()` - Element has CSS class
- `toHaveAttribute()` - Element has attribute
- `toHaveBeenCalledWith()` - Function called with args

## Coverage Thresholds

The project has coverage thresholds set at 70% for:
- Branches
- Functions
- Lines
- Statements

Files excluded from coverage:
- `src/main.jsx` (entry point)
- `src/vite-env.d.ts` (type definitions)
- `src/**/*.d.ts` (type definition files)

## Configuration Files

- `jest.config.js` - Main Jest configuration
- `src/setupTests.js` - Test setup and global mocks
- `src/__mocks__/fileMock.js` - Static asset mocks

## Troubleshooting

### Common Issues

1. **Module not found errors**: Check path aliases in `jest.config.js`
2. **CSS import errors**: Ensure `identity-obj-proxy` is installed
3. **Image import errors**: Check file mock configuration
4. **Async test failures**: Use `waitFor()` and proper async/await

### Debug Tips

- Use `screen.debug()` to see rendered HTML
- Use `console.log()` in tests for debugging
- Run single test files: `npm test -- Button.test.tsx`
- Run tests in watch mode for faster feedback

## Example Test Files

Check these example test files in the project:
- `src/components/ui/__tests__/button.test.tsx` - Component testing
- `src/components/__tests__/LoginForm.test.jsx` - Complex component with mocks
- `src/lib/__tests__/utils.test.ts` - Utility function testing
