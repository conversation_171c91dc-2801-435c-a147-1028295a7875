{"numFailedTestSuites": 10, "numFailedTests": 29, "numPassedTestSuites": 33, "numPassedTests": 403, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 4, "numTodoTests": 0, "numTotalTestSuites": 43, "numTotalTests": 432, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1751627577156, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["useApiCall"], "duration": 116, "failureDetails": [], "failureMessages": [], "fullName": "useApiCall handles successful API call with success message and callback", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "handles successful API call with success message and callback"}, {"ancestorTitles": ["useApiCall"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "useApiCall handles API error with error message, toast, and callback", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handles API error with error message, toast, and callback"}, {"ancestorTitles": ["useApiCall"], "duration": 14, "failureDetails": [{"message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\nReceived promise resolved instead of rejected\nResolved to value: \u001b[31mundefined\u001b[39m"}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\nReceived promise resolved instead of rejected\nResolved to value: \u001b[31mundefined\u001b[39m\n    at expect (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\expect\\build\\index.js:113:15)\n    at expect (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js:39:11)\n    at Generator.call (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js:2:1)\n    at Generator._invoke [as next] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js:2:1)\n    at _next (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js:2:1)\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js:2:1)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useApiCall prevents multiple simultaneous calls", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "prevents multiple simultaneous calls"}, {"ancestorTitles": ["useApiCall"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "useApiCall resets state with reset", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "resets state with reset"}, {"ancestorTitles": ["useApiCall"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "useApiCall does not show toast if showToast is false", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "does not show toast if showToast is false"}, {"ancestorTitles": ["useApiCall"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "useApiCall does not reset state if resetOnStart is false", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "does not reset state if resetOnStart is false"}], "endTime": 1751627583267, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museApiCall › prevents multiple simultaneous calls\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\n    Received promise resolved instead of rejected\n    Resolved to value: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 37 |\u001b[39m     \u001b[36mconst\u001b[39m onError \u001b[33m=\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 38 |\u001b[39m     \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useApiCall())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 39 |\u001b[39m     \u001b[36mawait\u001b[39m expect(\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 40 |\u001b[39m       act(\u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 41 |\u001b[39m         \u001b[36mawait\u001b[39m result\u001b[33m.\u001b[39mcurrent\u001b[33m.\u001b[39mexecute(apiCall\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 42 |\u001b[39m           onError\u001b[33m,\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat expect (\u001b[22m\u001b[2mnode_modules/expect/build/index.js\u001b[2m:113:15)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat expect (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useApiCall.test.js\u001b[39m\u001b[0m\u001b[2m:39:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.call (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useApiCall.test.js\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator._invoke [as next] (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useApiCall.test.js\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useApiCall.test.js\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useApiCall.test.js\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useApiCall.test.js\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useApiCall.test.js\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useApiCall.test.js", "startTime": 1751627579011, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1751627601912, "message": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Your test suite must contain at least one test.\n\n      \u001b[2mat onResult (\u001b[22mnode_modules/@jest/core/build/TestScheduler.js\u001b[2m:133:18)\u001b[22m\n      \u001b[2mat \u001b[22mnode_modules/@jest/core/build/TestScheduler.js\u001b[2m:254:19\u001b[22m\n      \u001b[2mat \u001b[22mnode_modules/emittery/index.js\u001b[2m:363:13\u001b[22m\n          at Array.map (<anonymous>)\n      \u001b[2mat Emittery.emit (\u001b[22mnode_modules/emittery/index.js\u001b[2m:361:23)\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\PropTypesValidation.test.jsx", "startTime": 1751627601912, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useIsMobile", "Initial State"], "duration": 204, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:43:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:43:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Initial State should return false for desktop width initially", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return false for desktop width initially"}, {"ancestorTitles": ["useIsMobile", "Initial State"], "duration": 41, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:54:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:54:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Initial State should return true for mobile width initially", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return true for mobile width initially"}, {"ancestorTitles": ["useIsMobile", "Initial State"], "duration": 22, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:65:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:65:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Initial State should return true for width exactly at mobile breakpoint", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return true for width exactly at mobile breakpoint"}, {"ancestorTitles": ["useIsMobile", "Initial State"], "duration": 43, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:76:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:76:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Initial State should return false for width exactly at desktop breakpoint", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return false for width exactly at desktop breakpoint"}, {"ancestorTitles": ["useIsMobile", "Media Query Setup"], "duration": 30, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:86:18\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:86:17)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Media Query Setup should create media query with correct breakpoint", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should create media query with correct breakpoint"}, {"ancestorTitles": ["useIsMobile", "Media Query Setup"], "duration": 23, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:94:18\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:94:17)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Media Query Setup should add event listener for media query changes", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should add event listener for media query changes"}, {"ancestorTitles": ["useIsMobile", "Media Query Setup"], "duration": 25, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:105:38\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:105:37)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Media Query Setup should remove event listener on unmount", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should remove event listener on unmount"}, {"ancestorTitles": ["useIsMobile", "Window Resize Handling"], "duration": 22, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:120:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:120:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Window Resize Handling should update when window width changes from desktop to mobile", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should update when window width changes from desktop to mobile"}, {"ancestorTitles": ["useIsMobile", "Window Resize Handling"], "duration": 16, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:140:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:140:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Window Resize Handling should update when window width changes from mobile to desktop", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should update when window width changes from mobile to desktop"}, {"ancestorTitles": ["useIsMobile", "Window Resize Handling"], "duration": 19, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:160:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:160:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Window Resize Handling should handle multiple resize events correctly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle multiple resize events correctly"}, {"ancestorTitles": ["useIsMobile", "Edge Cases"], "duration": 22, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:190:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:190:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Edge Cases should handle window width of 0", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle window width of 0"}, {"ancestorTitles": ["useIsMobile", "Edge Cases"], "duration": 21, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:201:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:201:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Edge Cases should handle very large window width", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle very large window width"}, {"ancestorTitles": ["useIsMobile", "Edge Cases"], "duration": 28, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:212:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:212:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Edge Cases should handle negative window width", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle negative window width"}, {"ancestorTitles": ["useIsMobile", "Edge Cases"], "duration": 17, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:223:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:223:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Edge Cases should handle fractional window width", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle fractional window width"}, {"ancestorTitles": ["useIsMobile", "Hook Lifecycle"], "duration": 29, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:233:48\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:233:47)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Hook Lifecycle should not cause memory leaks with multiple mounts/unmounts", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should not cause memory leaks with multiple mounts/unmounts"}, {"ancestorTitles": ["useIsMobile", "Hook Lifecycle"], "duration": 22, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:251:46\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:251:45)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Hook Lifecycle should maintain independent state across multiple hook instances", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should maintain independent state across multiple hook instances"}, {"ancestorTitles": ["useIsMobile", "Boolean Conversion"], "duration": 26, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:269:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:269:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Boolean Conversion should always return a boolean value", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should always return a boolean value"}, {"ancestorTitles": ["useIsMobile", "Boolean Conversion"], "duration": 18, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:283:37\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:283:36)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Boolean Conversion should handle undefined initial state correctly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle undefined initial state correctly"}, {"ancestorTitles": ["useIsMobile", "Breakpoint Constants"], "duration": 22, "failureDetails": [{}], "failureMessages": ["ReferenceError: useIsMobile is not defined\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:296:18\n    at TestComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:331:27)\n    at renderWithHooks (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at renderHook (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\react\\dist\\pure.js:340:7)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx:296:17)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "useIsMobile Breakpoint Constants should use 768px as the mobile breakpoint", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should use 768px as the mobile breakpoint"}], "endTime": 1751627584219, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Initial State › should return false for desktop width initially\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 41 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 42 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 43 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 45 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:43:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:43:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Initial State › should return true for mobile width initially\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 52 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 53 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 54 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 55 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 56 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 57 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:54:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:54:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Initial State › should return true for width exactly at mobile breakpoint\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 63 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 64 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 65 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 66 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 67 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 68 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:65:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:65:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Initial State › should return false for width exactly at desktop breakpoint\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 74 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 75 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 76 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 77 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 78 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 79 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:76:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:76:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Media Query Setup › should create media query with correct breakpoint\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 84 |\u001b[39m     test(\u001b[32m'should create media query with correct breakpoint'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 85 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 86 |\u001b[39m       renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 87 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 88 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 89 |\u001b[39m       expect(mockMatchMedia)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(\u001b[32m'(max-width: 767px)'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:86:18\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:86:17)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Media Query Setup › should add event listener for media query changes\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 92 |\u001b[39m     test(\u001b[32m'should add event listener for media query changes'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 93 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 94 |\u001b[39m       renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 95 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 96 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 97 |\u001b[39m       expect(mockMediaQueryList\u001b[33m.\u001b[39maddEventListener)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:94:18\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:94:17)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Media Query Setup › should remove event listener on unmount\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 103 |\u001b[39m     test(\u001b[32m'should remove event listener on unmount'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 104 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 105 |\u001b[39m       \u001b[36mconst\u001b[39m { unmount } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 106 |\u001b[39m       unmount()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 107 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 108 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:105:38\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:105:37)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Window Resize Handling › should update when window width changes from desktop to mobile\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 118 |\u001b[39m       \u001b[90m// Arrange\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 119 |\u001b[39m       window\u001b[33m.\u001b[39minnerWidth \u001b[33m=\u001b[39m \u001b[35m1024\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 120 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 121 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 122 |\u001b[39m       \u001b[90m// Initial state should be false (desktop)\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 123 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:120:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:120:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Window Resize Handling › should update when window width changes from mobile to desktop\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 138 |\u001b[39m       \u001b[90m// Arrange\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 139 |\u001b[39m       window\u001b[33m.\u001b[39minnerWidth \u001b[33m=\u001b[39m \u001b[35m375\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 140 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 141 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 142 |\u001b[39m       \u001b[90m// Initial state should be true (mobile)\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 143 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:140:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:140:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Window Resize Handling › should handle multiple resize events correctly\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m       \u001b[90m// Arrange\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m       window\u001b[33m.\u001b[39minnerWidth \u001b[33m=\u001b[39m \u001b[35m1024\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m       \u001b[36mconst\u001b[39m changeHandler \u001b[33m=\u001b[39m mockMediaQueryList\u001b[33m.\u001b[39maddEventListener\u001b[33m.\u001b[39mmock\u001b[33m.\u001b[39mcalls[\u001b[35m0\u001b[39m][\u001b[35m1\u001b[39m]\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m       \u001b[90m// Act & Assert - Multiple resize events\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:160:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:160:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Edge Cases › should handle window width of 0\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 188 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 189 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 190 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 191 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 192 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 193 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:190:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:190:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Edge Cases › should handle very large window width\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 199 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 200 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 201 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 202 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 203 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 204 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:201:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:201:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Edge Cases › should handle negative window width\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 210 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 211 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 212 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 213 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 214 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 215 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:212:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:212:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Edge Cases › should handle fractional window width\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 221 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 222 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 223 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 224 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 225 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 226 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// 767.5 < 768, so it's mobile\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:223:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:223:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Hook Lifecycle › should not cause memory leaks with multiple mounts/unmounts\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 231 |\u001b[39m     test(\u001b[32m'should not cause memory leaks with multiple mounts/unmounts'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 232 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 233 |\u001b[39m       \u001b[36mconst\u001b[39m { unmount\u001b[33m:\u001b[39m unmount1 } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 234 |\u001b[39m       \u001b[36mconst\u001b[39m { unmount\u001b[33m:\u001b[39m unmount2 } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 235 |\u001b[39m       \u001b[36mconst\u001b[39m { unmount\u001b[33m:\u001b[39m unmount3 } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 236 |\u001b[39m       \u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:233:48\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:233:47)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Hook Lifecycle › should maintain independent state across multiple hook instances\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 249 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 250 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 251 |\u001b[39m       \u001b[36mconst\u001b[39m { result\u001b[33m:\u001b[39m result1 } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 252 |\u001b[39m       \u001b[36mconst\u001b[39m { result\u001b[33m:\u001b[39m result2 } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 253 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 254 |\u001b[39m       \u001b[90m// Assert - Both instances should return the same value\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:251:46\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:251:45)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Boolean Conversion › should always return a boolean value\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 267 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 268 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 269 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 270 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 271 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 272 |\u001b[39m       expect(\u001b[36mtypeof\u001b[39m result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[32m'boolean'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:269:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:269:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Boolean Conversion › should handle undefined initial state correctly\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 281 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 282 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 283 |\u001b[39m       \u001b[36mconst\u001b[39m { result } \u001b[33m=\u001b[39m renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 284 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 285 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 286 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:283:37\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:283:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museIsMobile › Breakpoint Constants › should use 768px as the mobile breakpoint\u001b[39m\u001b[22m\n\n    ReferenceError: useIsMobile is not defined\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 294 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 295 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 296 |\u001b[39m       renderHook(() \u001b[33m=>\u001b[39m useIsMobile())\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 297 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 298 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 299 |\u001b[39m       expect(mockMatchMedia)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(\u001b[32m'(max-width: 767px)'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:296:18\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat TestComponent (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:331:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderHook (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:340:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/use-mobile.test.tsx\u001b[39m\u001b[0m\u001b[2m:296:17)\u001b[22m\u001b[2m\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-mobile.test.tsx", "startTime": 1751627578974, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useFormValidation"], "duration": 90, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should initialize with initial values", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should initialize with initial values"}, {"ancestorTitles": ["useFormValidation"], "duration": 30, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate email field (required)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate email field (required)"}, {"ancestorTitles": ["useFormValidation"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate email field (invalid)", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should validate email field (invalid)"}, {"ancestorTitles": ["useFormValidation"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate email field (valid)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate email field (valid)"}, {"ancestorTitles": ["useFormValidation"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate name field (required)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate name field (required)"}, {"ancestorTitles": ["useFormValidation"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate name field (min length)", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should validate name field (min length)"}, {"ancestorTitles": ["useFormValidation"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate name field (valid)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate name field (valid)"}, {"ancestorTitles": ["useFormValidation"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate mobile field (required)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate mobile field (required)"}, {"ancestorTitles": ["useFormValidation"], "duration": 26, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate mobile field (invalid)", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should validate mobile field (invalid)"}, {"ancestorTitles": ["useFormValidation"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate mobile field (valid)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate mobile field (valid)"}, {"ancestorTitles": ["useFormValidation"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate agreeToTerms field (required)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate agreeToTerms field (required)"}, {"ancestorTitles": ["useFormValidation"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate agreeToTerms field (valid)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should validate agreeToTerms field (valid)"}, {"ancestorTitles": ["useFormValidation"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should validate the whole form and set errors", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should validate the whole form and set errors"}, {"ancestorTitles": ["useFormValidation"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should clear error on input change", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should clear error on input change"}, {"ancestorTitles": ["useFormValidation"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should set touched state on blur", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should set touched state on blur"}, {"ancestorTitles": ["useFormValidation"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should reset form to initial values", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should reset form to initial values"}, {"ancestorTitles": ["useFormValidation"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "useFormValidation should set field value, error, and touched manually", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should set field value, error, and touched manually"}, {"ancestorTitles": ["useFormValidation"], "duration": 1053, "failureDetails": [{"matcherResult": {"actual": false, "expected": true, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\nIgnored nodes: comments, script, style\n\u001b[36m<html>\u001b[39m\n  \u001b[36m<head />\u001b[39m\n  \u001b[36m<body>\u001b[39m\n    \u001b[36m<div />\u001b[39m\n  \u001b[36m</body>\u001b[39m\n\u001b[36m</html>\u001b[39m\n    at toBe (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useFormValidation.test.js:231:38)\n    at runWithExpensiveErrorDiagnosticsDisabled (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\config.js:47:12)\n    at checkCallback (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:124:77)\n    at checkRealTimersCallback (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:118:16)\n    at Timeout.task [as _onTimeout] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jsdom\\lib\\jsdom\\browser\\Window.js:520:19)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)"], "fullName": "useFormValidation should return isValid as true only if there are no errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return isValid as true only if there are no errors"}], "endTime": 1751627584562, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1museFormValidation › should return isValid as true only if there are no errors\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32mtrue\u001b[39m\n    Received: \u001b[31mfalse\u001b[39m\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<html>\u001b[39m\n      \u001b[36m<head />\u001b[39m\n      \u001b[36m<body>\u001b[39m\n        \u001b[36m<div />\u001b[39m\n      \u001b[36m</body>\u001b[39m\n    \u001b[36m</html>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 229 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 230 |\u001b[39m     \u001b[36mawait\u001b[39m waitFor(() \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 231 |\u001b[39m       expect(result\u001b[33m.\u001b[39mcurrent\u001b[33m.\u001b[39misValid)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 232 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 233 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 234 |\u001b[39m })\u001b[33m;\u001b[39m \u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat toBe (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/hooks/useFormValidation.test.js\u001b[39m\u001b[0m\u001b[2m:231:38)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat runWithExpensiveErrorDiagnosticsDisabled (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/config.js\u001b[2m:47:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat checkCallback (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:124:77)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat checkRealTimersCallback (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:118:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Timeout.task [as _onTimeout] (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/browser/Window.js\u001b[2m:520:19)\u001b[22m\u001b[2m\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useFormValidation.test.js", "startTime": 1751627579044, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ConfirmPasswordInput"], "duration": 65, "failureDetails": [], "failureMessages": [], "fullName": "ConfirmPasswordInput renders BasePasswordInput with default label and autoComplete", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "renders BasePasswordInput with default label and autoComplete"}, {"ancestorTitles": ["ConfirmPasswordInput"], "duration": 25, "failureDetails": [], "failureMessages": [], "fullName": "ConfirmPasswordInput forwards all props to BasePasswordInput", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "forwards all props to BasePasswordInput"}, {"ancestorTitles": ["ConfirmPasswordInput"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "ConfirmPasswordInput overrides default label and autoComplete if provided", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "overrides default label and autoComplete if provided"}], "endTime": 1751627590478, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\ConfirmPasswordInput.test.jsx", "startTime": 1751627587194, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Profile Page", "Initial State"], "duration": 259, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Initial State should render with default user data", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render with default user data"}, {"ancestorTitles": ["Profile Page", "Initial State"], "duration": 84, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Initial State should start in view mode", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should start in view mode"}, {"ancestorTitles": ["Profile Page", "Component Rendering"], "duration": 32, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Rendering should render profile settings title", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render profile settings title"}, {"ancestorTitles": ["Profile Page", "Component Rendering"], "duration": 42, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Rendering should render back button", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render back button"}, {"ancestorTitles": ["Profile Page", "Component Rendering"], "duration": 36, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Rendering should render user avatar with initials", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render user avatar with initials"}, {"ancestorTitles": ["Profile Page", "Component Rendering"], "duration": 36, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Rendering should render user information in view mode", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render user information in view mode"}, {"ancestorTitles": ["Profile Page", "Component Rendering"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Rendering should render personal information section", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render personal information section"}, {"ancestorTitles": ["Profile Page", "Component Rendering"], "duration": 77, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Rendering should render member since date", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render member since date"}, {"ancestorTitles": ["Profile Page", "Edit Mode Toggle"], "duration": 638, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Edit Mode Toggle should enter edit mode when edit button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should enter edit mode when edit button is clicked"}, {"ancestorTitles": ["Profile Page", "Edit Mode Toggle"], "duration": 348, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Edit Mode Toggle should exit edit mode when cancel button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should exit edit mode when cancel button is clicked"}, {"ancestorTitles": ["Profile Page", "Edit Mode Toggle"], "duration": 728, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Edit Mode Toggle should reset form values when canceling edit", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should reset form values when canceling edit"}, {"ancestorTitles": ["Profile Page", "Form Handling"], "duration": 328, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Form Handling should update form values when typing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should update form values when typing"}, {"ancestorTitles": ["Profile Page", "Form Handling"], "duration": 520, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Form Handling should handle email field changes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle email field changes"}, {"ancestorTitles": ["Profile Page", "Form Handling"], "duration": 1435, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Form Handling should save changes when save button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should save changes when save button is clicked"}, {"ancestorTitles": ["Profile Page", "Form Handling"], "duration": 216, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Form Handling should show loading state during save", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show loading state during save"}, {"ancestorTitles": ["Profile Page", "Navigation"], "duration": 59, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Navigation should navigate to dashboard when back button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should navigate to dashboard when back button is clicked"}, {"ancestorTitles": ["Profile Page", "Component Structure"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Structure should render with proper container structure", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render with proper container structure"}, {"ancestorTitles": ["Profile Page", "Component Structure"], "duration": 69, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Component Structure should render form fields with correct properties", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should render form fields with correct properties"}, {"ancestorTitles": ["Profile Page", "Erro<PERSON>"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Error <PERSON> should render without crashing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render without crashing"}, {"ancestorTitles": ["Profile Page", "Erro<PERSON>"], "duration": 20, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Error Handling should handle component unmounting gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle component unmounting gracefully"}, {"ancestorTitles": ["Profile Page", "Erro<PERSON>"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Error Handling should handle multiple re-renders", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle multiple re-renders"}, {"ancestorTitles": ["Profile Page", "Erro<PERSON>"], "duration": 1190, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m\"Failed to update profile\"\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m\"Failed to update profile\"\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n\nIgnored nodes: comments, script, style\n\u001b[36m<html>\u001b[39m\n  \u001b[36m<head />\u001b[39m\n  \u001b[36m<body>\u001b[39m\n    \u001b[36m<div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"box\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mdata-position\u001b[39m=\u001b[32m\"static\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"app-bar\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"toolbar\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mdata-edge\u001b[39m=\u001b[32m\"start\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"icon-button\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<div\u001b[39m\n                \u001b[33mdata-testid\u001b[39m=\u001b[32m\"arrow-left-icon\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</button>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n              \u001b[33mdata-variant\u001b[39m=\u001b[32m\"h6\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mProfile Settings\u001b[0m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mdata-maxwidth\u001b[39m=\u001b[32m\"md\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"container\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-container\u001b[39m=\u001b[32m\"true\"\u001b[39m\n            \u001b[33mdata-spacing\u001b[39m=\u001b[32m\"3\"\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n              \u001b[33mdata-md\u001b[39m=\u001b[32m\"4\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n              \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<div\u001b[39m\n                \u001b[33mdata-testid\u001b[39m=\u001b[32m\"card\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"avatar\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[0mJD\u001b[0m\n                \u001b[36m</div>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                  \u001b[33mdata-variant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[0mJohn Doe\u001b[0m\n                \u001b[36m</div>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mcolor\u001b[39m=\u001b[32m\"textSecondary\"\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                  \u001b[33mdata-variant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[<EMAIL>\u001b[0m\n                \u001b[36m</div>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mcolor\u001b[39m=\u001b[32m\"textSecondary\"\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                  \u001b[33mdata-variant\u001b[39m=\u001b[32m\"caption\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[0mMember since \u001b[0m\n                  \u001b[0m1/15/2024\u001b[0m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</div>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n              \u001b[33mdata-md\u001b[39m=\u001b[32m\"8\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n              \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<div\u001b[39m\n                \u001b[33mdata-testid\u001b[39m=\u001b[32m\"card\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"card-content\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"box\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                      \u001b[33mdata-variant\u001b[39m=\u001b[32m\"h6\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mPersonal Information\u001b[0m\n                    \u001b[36m</div>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mdata-size\u001b[39m=\u001b[32m\"small\"\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"button\"\u001b[39m\n                      \u001b[33mdata-variant\u001b[39m=\u001b[32m\"outlined\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mEdit Profile\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"divider\"\u001b[39m\n                  \u001b[36m/>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mdata-container\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                    \u001b[33mdata-spacing\u001b[39m=\u001b[32m\"3\"\u001b[39m\n                    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                      \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                      \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[36m<input\u001b[39m\n                        \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                        \u001b[33mdata-label\u001b[39m=\u001b[32m\"Full Name\"\u001b[39m\n                        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                        \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                        \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Full Name\"\u001b[39m\n                        \u001b[33mvalue\u001b[39m=\u001b[32m\"John Doe\"\u001b[39m\n                      \u001b[36m/>\u001b[39m\n                    \u001b[36m</div>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                      \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                      \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[36m<input\u001b[39m\n                        \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                        \u001b[33mdata-label\u001b[39m=\u001b[32m\"Email Address\"\u001b[39m\n                        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                        \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                        \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Email Address\"\u001b[39m\n                        \u001b[33mvalue\u001b[39m=\u001b[32m\"<EMAIL>\"\u001b[39m\n                      \u001b[36m/>\u001b[39m\n                    \u001b[36m</div>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                      \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                      \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[36m<input\u001b[39m\n                        \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                        \u001b[33mdata-label\u001b[39m=\u001b[32m\"Phone Number\"\u001b[39m\n                        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                        \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                        \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Phone Number\"\u001b[39m\n                        \u001b[33mvalue\u001b[39m=\u001b[32m\"+****************\"\u001b[39m\n                      \u001b[36m/>\u001b[39m\n                    \u001b[36m</div>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                      \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                      \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[36m<input\u001b[39m\n                        \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                        \u001b[33mdata-label\u001b[39m=\u001b[32m\"Join Date\"\u001b[39m\n                        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                        \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                        \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Join Date\"\u001b[39m\n                        \u001b[33mvalue\u001b[39m=\u001b[32m\"1/15/2024\"\u001b[39m\n                      \u001b[36m/>\u001b[39m\n                    \u001b[36m</div>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</div>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</body>\u001b[39m\n\u001b[36m</html>\u001b[39m\n    at toHaveBeenCalledWith (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\Profile.test.jsx:470:29)\n    at runWithExpensiveErrorDiagnosticsDisabled (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\config.js:47:12)\n    at checkCallback (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:124:77)\n    at MutationObserver.checkRealTimersCallback (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:118:16)\n    at MutationObserver.invokeTheCallbackFunction (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\MutationCallback.js:19:26)\n    at notifyMutationObservers (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\mutation-observers.js:160:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\mutation-observers.js:133:5"], "fullName": "Profile Page Error Handling should handle save operation failure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle save operation failure"}, {"ancestorTitles": ["Profile Page", "Erro<PERSON>"], "duration": 118, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Error Handling should disable buttons during loading state", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should disable buttons during loading state"}, {"ancestorTitles": ["Profile Page", "Erro<PERSON>"], "duration": 1052, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Error Handling should handle all form field updates", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle all form field updates"}, {"ancestorTitles": ["Profile Page", "Erro<PERSON>"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Profile Page Error <PERSON> should handle avatar initials for different name formats", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle avatar initials for different name formats"}], "endTime": 1751627590723, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mProfile Page › Error Handling › should handle save operation failure\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32m\"Failed to update profile\"\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<html>\u001b[39m\n      \u001b[36m<head />\u001b[39m\n      \u001b[36m<body>\u001b[39m\n        \u001b[36m<div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"box\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mdata-position\u001b[39m=\u001b[32m\"static\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"app-bar\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<div\u001b[39m\n                \u001b[33mdata-testid\u001b[39m=\u001b[32m\"toolbar\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<button\u001b[39m\n                  \u001b[33mdata-edge\u001b[39m=\u001b[32m\"start\"\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"icon-button\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"arrow-left-icon\"\u001b[39m\n                  \u001b[36m/>\u001b[39m\n                \u001b[36m</button>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                  \u001b[33mdata-variant\u001b[39m=\u001b[32m\"h6\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[0mProfile Settings\u001b[0m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</div>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mdata-maxwidth\u001b[39m=\u001b[32m\"md\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"container\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<div\u001b[39m\n                \u001b[33mdata-container\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                \u001b[33mdata-spacing\u001b[39m=\u001b[32m\"3\"\u001b[39m\n                \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                  \u001b[33mdata-md\u001b[39m=\u001b[32m\"4\"\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                  \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"card\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"avatar\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mJD\u001b[0m\n                    \u001b[36m</div>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                      \u001b[33mdata-variant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mJohn Doe\u001b[0m\n                    \u001b[36m</div>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mcolor\u001b[39m=\u001b[32m\"textSecondary\"\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                      \u001b[33mdata-variant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[<EMAIL>\u001b[0m\n                    \u001b[36m</div>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mcolor\u001b[39m=\u001b[32m\"textSecondary\"\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                      \u001b[33mdata-variant\u001b[39m=\u001b[32m\"caption\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mMember since \u001b[0m\n                      \u001b[0m1/15/2024\u001b[0m\n                    \u001b[36m</div>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                \u001b[36m</div>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                  \u001b[33mdata-md\u001b[39m=\u001b[32m\"8\"\u001b[39m\n                  \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                  \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"card\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<div\u001b[39m\n                      \u001b[33mdata-testid\u001b[39m=\u001b[32m\"card-content\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[36m<div\u001b[39m\n                        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"box\"\u001b[39m\n                      \u001b[36m>\u001b[39m\n                        \u001b[36m<div\u001b[39m\n                          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"typography\"\u001b[39m\n                          \u001b[33mdata-variant\u001b[39m=\u001b[32m\"h6\"\u001b[39m\n                        \u001b[36m>\u001b[39m\n                          \u001b[0mPersonal Information\u001b[0m\n                        \u001b[36m</div>\u001b[39m\n                        \u001b[36m<button\u001b[39m\n                          \u001b[33mdata-size\u001b[39m=\u001b[32m\"small\"\u001b[39m\n                          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"button\"\u001b[39m\n                          \u001b[33mdata-variant\u001b[39m=\u001b[32m\"outlined\"\u001b[39m\n                        \u001b[36m>\u001b[39m\n                          \u001b[0mEdit Profile\u001b[0m\n                        \u001b[36m</button>\u001b[39m\n                      \u001b[36m</div>\u001b[39m\n                      \u001b[36m<div\u001b[39m\n                        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"divider\"\u001b[39m\n                      \u001b[36m/>\u001b[39m\n                      \u001b[36m<div\u001b[39m\n                        \u001b[33mdata-container\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                        \u001b[33mdata-spacing\u001b[39m=\u001b[32m\"3\"\u001b[39m\n                        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                      \u001b[36m>\u001b[39m\n                        \u001b[36m<div\u001b[39m\n                          \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                          \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                          \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                        \u001b[36m>\u001b[39m\n                          \u001b[36m<input\u001b[39m\n                            \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                            \u001b[33mdata-label\u001b[39m=\u001b[32m\"Full Name\"\u001b[39m\n                            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                            \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Full Name\"\u001b[39m\n                            \u001b[33mvalue\u001b[39m=\u001b[32m\"John Doe\"\u001b[39m\n                          \u001b[36m/>\u001b[39m\n                        \u001b[36m</div>\u001b[39m\n                        \u001b[36m<div\u001b[39m\n                          \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                          \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                          \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                        \u001b[36m>\u001b[39m\n                          \u001b[36m<input\u001b[39m\n                            \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                            \u001b[33mdata-label\u001b[39m=\u001b[32m\"Email Address\"\u001b[39m\n                            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                            \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Email Address\"\u001b[39m\n                            \u001b[33mvalue\u001b[39m=\u001b[32m\"<EMAIL>\"\u001b[39m\n                          \u001b[36m/>\u001b[39m\n                        \u001b[36m</div>\u001b[39m\n                        \u001b[36m<div\u001b[39m\n                          \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                          \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                          \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                        \u001b[36m>\u001b[39m\n                          \u001b[36m<input\u001b[39m\n                            \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                            \u001b[33mdata-label\u001b[39m=\u001b[32m\"Phone Number\"\u001b[39m\n                            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                            \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Phone Number\"\u001b[39m\n                            \u001b[33mvalue\u001b[39m=\u001b[32m\"+****************\"\u001b[39m\n                          \u001b[36m/>\u001b[39m\n                        \u001b[36m</div>\u001b[39m\n                        \u001b[36m<div\u001b[39m\n                          \u001b[33mdata-item\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                          \u001b[33mdata-sm\u001b[39m=\u001b[32m\"6\"\u001b[39m\n                          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"grid\"\u001b[39m\n                          \u001b[33mdata-xs\u001b[39m=\u001b[32m\"12\"\u001b[39m\n                        \u001b[36m>\u001b[39m\n                          \u001b[36m<input\u001b[39m\n                            \u001b[33mdata-fullwidth\u001b[39m=\u001b[32m\"true\"\u001b[39m\n                            \u001b[33mdata-label\u001b[39m=\u001b[32m\"Join Date\"\u001b[39m\n                            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"text-field\"\u001b[39m\n                            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                            \u001b[33mplaceholder\u001b[39m=\u001b[32m\"Join Date\"\u001b[39m\n                            \u001b[33mvalue\u001b[39m=\u001b[32m\"1/15/2024\"\u001b[39m\n                          \u001b[36m/>\u001b[39m\n                        \u001b[36m</div>\u001b[39m\n                      \u001b[36m</div>\u001b[39m\n                    \u001b[36m</div>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</div>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</body>\u001b[39m\n    \u001b[36m</html>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 468 |\u001b[39m       \u001b[90m// Assert - Should handle error gracefully\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 469 |\u001b[39m       \u001b[36mawait\u001b[39m waitFor(() \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 470 |\u001b[39m         expect(toast\u001b[33m.\u001b[39merror)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(\u001b[32m'Failed to update profile'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 471 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 472 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 473 |\u001b[39m       \u001b[90m// Restore original Promise\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat toHaveBeenCalledWith (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/pages/Profile.test.jsx\u001b[39m\u001b[0m\u001b[2m:470:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat runWithExpensiveErrorDiagnosticsDisabled (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/config.js\u001b[2m:47:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat checkCallback (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:124:77)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat MutationObserver.checkRealTimersCallback (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:118:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat MutationObserver.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/MutationCallback.js\u001b[2m:19:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat notifyMutationObservers (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/mutation-observers.js\u001b[2m:160:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/mutation-observers.js\u001b[2m:133:5\u001b[22m\u001b[2m\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\Profile.test.jsx", "startTime": 1751627578969, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ResetPassword Page", "Token Validation"], "duration": 182, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Token Validation should show error when no token is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show error when no token is provided"}, {"ancestorTitles": ["ResetPassword Page", "Token Validation"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Token Validation should show form when token is present", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should show form when token is present"}, {"ancestorTitles": ["ResetPassword Page", "Token Validation"], "duration": 33, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Token Validation should show form when valid token is provided", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should show form when valid token is provided"}, {"ancestorTitles": ["ResetPassword Page", "Form Rendering"], "duration": 184, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Form Rendering should render password reset form", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should render password reset form"}, {"ancestorTitles": ["ResetPassword Page", "Form Rendering"], "duration": 49, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Form Rendering should render form elements", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render form elements"}, {"ancestorTitles": ["ResetPassword Page", "Form Validation"], "duration": 316, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Form Validation should show password validation errors", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should show password validation errors"}, {"ancestorTitles": ["ResetPassword Page", "Form Validation"], "duration": 716, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Form Validation should show password mismatch error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should show password mismatch error"}, {"ancestorTitles": ["ResetPassword Page", "Form Validation"], "duration": 636, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Form Validation should enable submit button when passwords are valid and match", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should enable submit button when passwords are valid and match"}, {"ancestorTitles": ["ResetPassword Page", "API Integration"], "duration": 704, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page API Integration should submit form with valid data", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should submit form with valid data"}, {"ancestorTitles": ["ResetPassword Page", "API Integration"], "duration": 2042, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page API Integration should handle API success and redirect", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should handle API success and redirect"}, {"ancestorTitles": ["ResetPassword Page", "API Integration"], "duration": 630, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page API Integration should handle API error", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle API error"}, {"ancestorTitles": ["ResetPassword Page", "API Integration"], "duration": 689, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page API Integration should handle network error", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle network error"}, {"ancestorTitles": ["ResetPassword Page", "Component Structure"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Component Structure should render with proper structure", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render with proper structure"}, {"ancestorTitles": ["ResetPassword Page", "Component Structure"], "duration": 32, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Component Structure should have form elements with correct attributes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should have form elements with correct attributes"}, {"ancestorTitles": ["ResetPassword Page", "Component Structure"], "duration": 21, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Component Structure should handle component lifecycle", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle component lifecycle"}, {"ancestorTitles": ["ResetPassword Page", "Component Structure"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Component Structure should disable form when token is invalid", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should disable form when token is invalid"}, {"ancestorTitles": ["ResetPassword Page", "Button Disabled State"], "duration": 25, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Button Disabled State should disable submit button when token is loading", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should disable submit button when token is loading"}, {"ancestorTitles": ["ResetPassword Page", "Button Disabled State"], "duration": 45, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Button Disabled State should disable submit button when there is a token error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should disable submit button when there is a token error"}, {"ancestorTitles": ["ResetPassword Page", "Button Disabled State"], "duration": 78, "failureDetails": [], "failureMessages": [], "fullName": "ResetPassword Page Button Disabled State should disable submit button when passwords are empty or invalid", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should disable submit button when passwords are empty or invalid"}], "endTime": 1751627591812, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\ResetPassword.test.jsx", "startTime": 1751627583635, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["CreatePassword Page", "Token Validation"], "duration": 358, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Token Validation should show error when no token is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show error when no token is provided"}, {"ancestorTitles": ["CreatePassword Page", "Token Validation"], "duration": 25, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Token Validation should show verifying message initially when token is present", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show verifying message initially when token is present"}, {"ancestorTitles": ["CreatePassword Page", "Token Validation"], "duration": 45, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Token Validation should fetch email when valid token is provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should fetch email when valid token is provided"}, {"ancestorTitles": ["CreatePassword Page", "Token Validation"], "duration": 41, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Token Validation should handle already activated user", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle already activated user"}, {"ancestorTitles": ["CreatePassword Page", "Token Validation"], "duration": 61, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Token Validation should handle invalid token error", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle invalid token error"}, {"ancestorTitles": ["CreatePassword Page", "Token Validation"], "duration": 51, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Token Validation should handle network error during token validation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle network error during token validation"}, {"ancestorTitles": ["CreatePassword Page", "Form Rendering"], "duration": 35, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Form Rendering should render create password form", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render create password form"}, {"ancestorTitles": ["CreatePassword Page", "Form Rendering"], "duration": 23, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Form Rendering should render form elements with correct attributes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render form elements with correct attributes"}, {"ancestorTitles": ["CreatePassword Page", "Form Rendering"], "duration": 144, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Form Rendering should render submit button", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render submit button"}, {"ancestorTitles": ["CreatePassword Page", "Form Validation"], "duration": 277, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Form Validation should show password validation errors", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should show password validation errors"}, {"ancestorTitles": ["CreatePassword Page", "Form Validation"], "duration": 700, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Form Validation should show password mismatch error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should show password mismatch error"}, {"ancestorTitles": ["CreatePassword Page", "Form Validation"], "duration": 556, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Form Validation should enable submit button when passwords are valid and match", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should enable submit button when passwords are valid and match"}, {"ancestorTitles": ["CreatePassword Page", "Component Structure"], "duration": 44, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Component Structure should render with proper structure", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render with proper structure"}, {"ancestorTitles": ["CreatePassword Page", "Component Structure"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Component Structure should handle component lifecycle", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle component lifecycle"}, {"ancestorTitles": ["CreatePassword Page", "API Integration"], "duration": 657, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page API Integration should submit form with valid data", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should submit form with valid data"}, {"ancestorTitles": ["CreatePassword Page", "API Integration"], "duration": 625, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page API Integration should handle API success and authenticate user", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle API success and authenticate user"}, {"ancestorTitles": ["CreatePassword Page", "API Integration"], "duration": 647, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page API Integration should handle API error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle API error"}, {"ancestorTitles": ["CreatePassword Page", "API Integration"], "duration": 567, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page API Integration should handle network error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle network error"}, {"ancestorTitles": ["CreatePassword Page", "Erro<PERSON>"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Error Handling should render without crashing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render without crashing"}, {"ancestorTitles": ["CreatePassword Page", "Erro<PERSON>"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Error Handling should handle missing token gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle missing token gracefully"}, {"ancestorTitles": ["CreatePassword Page", "Erro<PERSON>"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Error Handling should handle API errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle API errors gracefully"}, {"ancestorTitles": ["CreatePassword Page", "Erro<PERSON>"], "duration": 34, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Error Handling should disable form when email verification fails", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should disable form when email verification fails"}, {"ancestorTitles": ["CreatePassword Page", "Side Effects"], "duration": 583, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Side Effects should set localStorage tokens on successful password creation", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should set localStorage tokens on successful password creation"}, {"ancestorTitles": ["CreatePassword Page", "Side Effects"], "duration": 1025, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Side Effects should navigate to /dashboard after successful password creation", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should navigate to /dashboard after successful password creation"}, {"ancestorTitles": ["CreatePassword Page", "Button Disabled State"], "duration": 23, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Button Disabled State should disable submit button when email is loading", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should disable submit button when email is loading"}, {"ancestorTitles": ["CreatePassword Page", "Button Disabled State"], "duration": 93, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Button Disabled State should disable submit button when there is an email error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should disable submit button when there is an email error"}, {"ancestorTitles": ["CreatePassword Page", "Button Disabled State"], "duration": 35, "failureDetails": [], "failureMessages": [], "fullName": "CreatePassword Page Button Disabled State should disable submit button when passwords are empty or invalid", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should disable submit button when passwords are empty or invalid"}], "endTime": 1751627591817, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\CreatePassword.test.jsx", "startTime": 1751627583385, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["LoginForm Real Implementation"], "duration": 904, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm Real Implementation renders login form with all required fields", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "renders login form with all required fields"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 940, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm Real Implementation updates form data when user types in fields", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "updates form data when user types in fields"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 1150, "failureDetails": [{"name": "TestingLibraryElementError"}], "failureMessages": ["Error: Unable to find an element by: [data-testid=\"error-password\"]\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<form\u001b[39m\n      \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<img\u001b[39m\n          \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n          \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n          \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mWelcome Back\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign in to your account to continue\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mGoogle Login\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mor continue with email\u001b[0m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mEmail Address\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n        \u001b[36m<span\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"error-email-address\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mEmail is required\u001b[0m\n        \u001b[36m</span>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mPassword\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mForgot password?\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<button\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign In\u001b[0m\n      \u001b[36m</button>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mDon't have an account?\u001b[0m\n        \u001b[0m \u001b[0m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mSign up\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</form>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at waitForWrapper (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:163:27)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:127:18\n    at Generator.call (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at Generator._invoke [as next] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)"], "fullName": "LoginForm Real Implementation shows validation errors for empty fields", "invocations": 1, "location": null, "numPassingAsserts": 17, "retryReasons": [], "status": "failed", "title": "shows validation errors for empty fields"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 1277, "failureDetails": [{"name": "TestingLibraryElementError"}], "failureMessages": ["Error: Unable to find an element by: [data-testid=\"error-password\"]\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<form\u001b[39m\n      \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<img\u001b[39m\n          \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n          \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n          \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mWelcome Back\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign in to your account to continue\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mGoogle Login\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mor continue with email\u001b[0m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mEmail Address\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n        \u001b[36m<span\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"error-email-address\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mEmail is required\u001b[0m\n        \u001b[36m</span>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mPassword\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"123\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n        \u001b[36m<span>\u001b[39m\n          \u001b[36m<button>\u001b[39m\n            \u001b[36m<span>\u001b[39m\n              \u001b[0mEye Icon\u001b[0m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</span>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mForgot password?\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<button\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign In\u001b[0m\n      \u001b[36m</button>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mDon't have an account?\u001b[0m\n        \u001b[0m \u001b[0m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mSign up\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</form>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at waitForWrapper (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:163:27)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:148:18\n    at Generator.call (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at Generator._invoke [as next] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)"], "fullName": "LoginForm Real Implementation shows validation error for short password", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "shows validation error for short password"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 752, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm Real Implementation calls login function with correct credentials on valid form submission", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls login function with correct credentials on valid form submission"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 599, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm Real Implementation shows success toast on successful login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows success toast on successful login"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 643, "failureDetails": [{}], "failureMessages": ["Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\n\nCheck the render method of `AlertMessage`.\n    at createFiberFromTypeAndProps (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:28478:17)\n    at createFiberFromElement (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:28504:15)\n    at reconcileSingleElement (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:13986:23)\n    at reconcileChildFibers (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:14044:35)\n    at reconcileChildren (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:19193:28)\n    at updateFunctionComponent (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:19649:3)\n    at beginWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:21640:16)\n    at beginWork$1 (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at workLoop (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\scheduler\\cjs\\scheduler.development.js:266:34)\n    at flushWork (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\scheduler\\cjs\\scheduler.development.js:239:14)\n    at performWorkUntilDeadline (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\scheduler\\cjs\\scheduler.development.js:533:21)\n    at Timeout.task [as _onTimeout] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jsdom\\lib\\jsdom\\browser\\Window.js:520:19)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)"], "fullName": "LoginForm Real Implementation shows error toast on failed login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "shows error toast on failed login"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 1117, "failureDetails": [{"name": "TestingLibraryElementError"}], "failureMessages": ["Error: Unable to find an element by: [data-testid=\"error-password\"]\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<form\u001b[39m\n      \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<img\u001b[39m\n          \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n          \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n          \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mWelcome Back\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign in to your account to continue\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mGoogle Login\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mor continue with email\u001b[0m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mEmail Address\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n        \u001b[36m<span\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"error-email-address\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mEmail is required\u001b[0m\n        \u001b[36m</span>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mPassword\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mForgot password?\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<button\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign In\u001b[0m\n      \u001b[36m</button>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mDon't have an account?\u001b[0m\n        \u001b[0m \u001b[0m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mSign up\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</form>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at waitForWrapper (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:163:27)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:236:18\n    at Generator.call (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at Generator._invoke [as next] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)"], "fullName": "LoginForm Real Implementation clears field errors when user starts typing", "invocations": 1, "location": null, "numPassingAsserts": 17, "retryReasons": [], "status": "failed", "title": "clears field errors when user starts typing"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 317, "failureDetails": [{"name": "TestingLibraryElementError"}], "failureMessages": ["TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name \"Eye Icon\"\n\nHere are the accessible roles:\n\n  img:\n\n  Name \"Brand Logo\":\n  \u001b[36m<img\u001b[39m\n    \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n    \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  --------------------------------------------------\n  textbox:\n\n  Name \"\":\n  \u001b[36m<input\u001b[39m\n    \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n    \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  --------------------------------------------------\n  button:\n\n  Name \"Forgot password?\":\n  \u001b[36m<button\u001b[39m\n    \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n    \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  Name \"Sign In\":\n  \u001b[36m<button\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n    \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  Name \"Sign up\":\n  \u001b[36m<button\u001b[39m\n    \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  --------------------------------------------------\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<form\u001b[39m\n      \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<img\u001b[39m\n          \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n          \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n          \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mWelcome Back\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign in to your account to continue\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mGoogle Login\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mor continue with email\u001b[0m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mEmail Address\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mPassword\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mForgot password?\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<button\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign In\u001b[0m\n      \u001b[36m</button>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mDon't have an account?\u001b[0m\n        \u001b[0m \u001b[0m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mSign up\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</form>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at getByRole (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:264:33)\n    at Generator.call (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at Generator._invoke [as next] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at _next (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:2:1)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "LoginForm Real Implementation toggles password visibility", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "toggles password visibility"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 104, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm Real Implementation calls onSwitchToSignUp when sign up link is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onSwitchToSignUp when sign up link is clicked"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 104, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm Real Implementation calls onSwitchToForgotPassword when forgot password link is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onSwitchToForgotPassword when forgot password link is clicked"}, {"ancestorTitles": ["LoginForm Real Implementation"], "duration": 384, "failureDetails": [{"name": "TestingLibraryElementError"}], "failureMessages": ["TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name `/loading/i`\n\nHere are the accessible roles:\n\n  img:\n\n  Name \"Brand Logo\":\n  \u001b[36m<img\u001b[39m\n    \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n    \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  --------------------------------------------------\n  textbox:\n\n  Name \"\":\n  \u001b[36m<input\u001b[39m\n    \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n    \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n    \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n    \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  --------------------------------------------------\n  button:\n\n  Name \"Forgot password?\":\n  \u001b[36m<button\u001b[39m\n    \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n    \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  Name \"Sign In\":\n  \u001b[36m<button\u001b[39m\n    \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n    \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  Name \"Sign up\":\n  \u001b[36m<button\u001b[39m\n    \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n    \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\n  --------------------------------------------------\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<form\u001b[39m\n      \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<img\u001b[39m\n          \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n          \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n          \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mWelcome Back\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign in to your account to continue\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mGoogle Login\u001b[0m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mor continue with email\u001b[0m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mEmail Address\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n          \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label>\u001b[39m\n          \u001b[0mPassword\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n          \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n          \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mForgot password?\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<button\u001b[39m\n        \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSign In\u001b[0m\n      \u001b[36m</button>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mDon't have an account?\u001b[0m\n        \u001b[0m \u001b[0m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mSign up\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</form>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at Object.getByRole (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx:318:33)\n    at Promise.then.completed (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "LoginForm Real Implementation shows loading state when isLoading is true", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "shows loading state when isLoading is true"}], "endTime": 1751627592028, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mLoginForm Real Implementation › shows validation errors for empty fields\u001b[39m\u001b[22m\n\n    Unable to find an element by: [data-testid=\"error-password\"]\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<body>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<form\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<img\u001b[39m\n              \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n              \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n              \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mWelcome Back\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign in to your account to continue\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGoogle Login\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mor continue with email\u001b[0m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mEmail Address\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"error-email-address\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mEmail is required\u001b[0m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mPassword\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mForgot password?\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign In\u001b[0m\n          \u001b[36m</button>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mDon't have an account?\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mSign up\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</form>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</body>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 125 |\u001b[39m     \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mclick(submitButton)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 126 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 127 |\u001b[39m     \u001b[36mawait\u001b[39m waitFor(() \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 128 |\u001b[39m       expect(screen\u001b[33m.\u001b[39mgetByTestId(\u001b[32m'error-email-address'\u001b[39m))\u001b[33m.\u001b[39mtoHaveTextContent(\u001b[32m'Email is required'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 129 |\u001b[39m       expect(screen\u001b[33m.\u001b[39mgetByTestId(\u001b[32m'error-password'\u001b[39m))\u001b[33m.\u001b[39mtoHaveTextContent(\u001b[32m'Password is required'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 130 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat waitForWrapper (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:163:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:127:18\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.call (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator._invoke [as next] (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mLoginForm Real Implementation › shows validation error for short password\u001b[39m\u001b[22m\n\n    Unable to find an element by: [data-testid=\"error-password\"]\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<body>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<form\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<img\u001b[39m\n              \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n              \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n              \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mWelcome Back\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign in to your account to continue\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGoogle Login\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mor continue with email\u001b[0m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mEmail Address\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"error-email-address\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mEmail is required\u001b[0m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mPassword\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"123\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<span>\u001b[39m\n              \u001b[36m<button>\u001b[39m\n                \u001b[36m<span>\u001b[39m\n                  \u001b[0mEye Icon\u001b[0m\n                \u001b[36m</span>\u001b[39m\n              \u001b[36m</button>\u001b[39m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mForgot password?\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign In\u001b[0m\n          \u001b[36m</button>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mDon't have an account?\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mSign up\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</form>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</body>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 146 |\u001b[39m     \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mclick(submitButton)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 147 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 148 |\u001b[39m     \u001b[36mawait\u001b[39m waitFor(() \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 149 |\u001b[39m       expect(screen\u001b[33m.\u001b[39mgetByTestId(\u001b[32m'error-password'\u001b[39m))\u001b[33m.\u001b[39mtoHaveTextContent(\u001b[32m'Password must be at least 6 characters'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 150 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 151 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat waitForWrapper (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:163:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:148:18\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.call (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator._invoke [as next] (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mLoginForm Real Implementation › shows error toast on failed login\u001b[39m\u001b[22m\n\n    Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\n\n    Check the render method of `AlertMessage`.\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat createFiberFromTypeAndProps (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:28478:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat createFiberFromElement (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:28504:15)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat reconcileSingleElement (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:13986:23)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat reconcileChildFibers (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:14044:35)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat reconcileChildren (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:19193:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat updateFunctionComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:19649:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21640:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoop (\u001b[22m\u001b[2mnode_modules/scheduler/cjs/scheduler.development.js\u001b[2m:266:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushWork (\u001b[22m\u001b[2mnode_modules/scheduler/cjs/scheduler.development.js\u001b[2m:239:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performWorkUntilDeadline (\u001b[22m\u001b[2mnode_modules/scheduler/cjs/scheduler.development.js\u001b[2m:533:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Timeout.task [as _onTimeout] (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/browser/Window.js\u001b[2m:520:19)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mLoginForm Real Implementation › clears field errors when user starts typing\u001b[39m\u001b[22m\n\n    Unable to find an element by: [data-testid=\"error-password\"]\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<body>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<form\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<img\u001b[39m\n              \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n              \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n              \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mWelcome Back\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign in to your account to continue\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGoogle Login\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mor continue with email\u001b[0m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mEmail Address\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"error-email-address\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mEmail is required\u001b[0m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mPassword\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mForgot password?\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign In\u001b[0m\n          \u001b[36m</button>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mDon't have an account?\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mSign up\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</form>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</body>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 234 |\u001b[39m     \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mclick(submitButton)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 235 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 236 |\u001b[39m     \u001b[36mawait\u001b[39m waitFor(() \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 237 |\u001b[39m       expect(screen\u001b[33m.\u001b[39mgetByTestId(\u001b[32m'error-email-address'\u001b[39m))\u001b[33m.\u001b[39mtoBeInTheDocument()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 238 |\u001b[39m       expect(screen\u001b[33m.\u001b[39mgetByTestId(\u001b[32m'error-password'\u001b[39m))\u001b[33m.\u001b[39mtoBeInTheDocument()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 239 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat waitForWrapper (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:163:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:236:18\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.call (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator._invoke [as next] (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mLoginForm Real Implementation › toggles password visibility\u001b[39m\u001b[22m\n\n    TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name \"Eye Icon\"\n\n    Here are the accessible roles:\n\n      img:\n\n      Name \"Brand Logo\":\n      \u001b[36m<img\u001b[39m\n        \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n        \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n        \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      --------------------------------------------------\n      textbox:\n\n      Name \"\":\n      \u001b[36m<input\u001b[39m\n        \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n        \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      --------------------------------------------------\n      button:\n\n      Name \"Forgot password?\":\n      \u001b[36m<button\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      Name \"Sign In\":\n      \u001b[36m<button\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      Name \"Sign up\":\n      \u001b[36m<button\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      --------------------------------------------------\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<body>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<form\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<img\u001b[39m\n              \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n              \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n              \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mWelcome Back\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign in to your account to continue\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGoogle Login\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mor continue with email\u001b[0m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mEmail Address\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mPassword\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mForgot password?\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign In\u001b[0m\n          \u001b[36m</button>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mDon't have an account?\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mSign up\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</form>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</body>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 262 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 263 |\u001b[39m     \u001b[36mconst\u001b[39m passwordInput \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39mgetByTestId(\u001b[32m'input-password'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 264 |\u001b[39m     \u001b[36mconst\u001b[39m toggleButton \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Eye Icon'\u001b[39m })\u001b[33m;\u001b[39m \u001b[90m// IconButton with Eye Icon\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 265 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 266 |\u001b[39m     expect(passwordInput\u001b[33m.\u001b[39mtype)\u001b[33m.\u001b[39mtoBe(\u001b[32m'password'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 267 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.getElementError (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/config.js\u001b[2m:37:19)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/query-helpers.js\u001b[2m:76:38\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/query-helpers.js\u001b[2m:52:17\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/query-helpers.js\u001b[2m:95:19\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getByRole (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:264:33)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.call (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator._invoke [as next] (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mLoginForm Real Implementation › shows loading state when isLoading is true\u001b[39m\u001b[22m\n\n    TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name `/loading/i`\n\n    Here are the accessible roles:\n\n      img:\n\n      Name \"Brand Logo\":\n      \u001b[36m<img\u001b[39m\n        \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n        \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n        \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      --------------------------------------------------\n      textbox:\n\n      Name \"\":\n      \u001b[36m<input\u001b[39m\n        \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n        \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n        \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n        \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      --------------------------------------------------\n      button:\n\n      Name \"Forgot password?\":\n      \u001b[36m<button\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      Name \"Sign In\":\n      \u001b[36m<button\u001b[39m\n        \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n        \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      Name \"Sign up\":\n      \u001b[36m<button\u001b[39m\n        \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n      \u001b[36m/>\u001b[39m\n\n      --------------------------------------------------\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<body>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<form\u001b[39m\n          \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<img\u001b[39m\n              \u001b[33malt\u001b[39m=\u001b[32m\"Brand Logo\"\u001b[39m\n              \u001b[33msrc\u001b[39m=\u001b[32m\"/brand-logo.svg\"\u001b[39m\n              \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 80px; height: 80px; display: block; margin: 0px auto 16px auto;\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mcomponent\u001b[39m=\u001b[32m\"h1\"\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"h5\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mWelcome Back\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign in to your account to continue\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mdata-testid\u001b[39m=\u001b[32m\"google-login-button\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGoogle Login\u001b[0m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mor continue with email\u001b[0m\n            \u001b[36m</div>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mEmail Address\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n              \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label>\u001b[39m\n              \u001b[0mPassword\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mautocomplete\u001b[39m=\u001b[32m\"current-password\"\u001b[39m\n              \u001b[33mdata-testid\u001b[39m=\u001b[32m\"input-password\"\u001b[39m\n              \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"password\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mForgot password?\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"contained\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSign In\u001b[0m\n          \u001b[36m</button>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n            \u001b[33mvariant\u001b[39m=\u001b[32m\"body2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mDon't have an account?\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mcomponent\u001b[39m=\u001b[32m\"button\"\u001b[39m\n              \u001b[33msx\u001b[39m=\u001b[32m\"[object Object]\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mSign up\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</form>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</body>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 316 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 317 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 318 |\u001b[39m     \u001b[36mconst\u001b[39m submitButton \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[35m/loading/i\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 319 |\u001b[39m     expect(submitButton)\u001b[33m.\u001b[39mtoBeDisabled()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 320 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 321 |\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.getElementError (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/config.js\u001b[2m:37:19)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/query-helpers.js\u001b[2m:76:38\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/query-helpers.js\u001b[2m:52:17\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/query-helpers.js\u001b[2m:95:19\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.getByRole (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/LoginForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:318:33)\u001b[22m\u001b[2m\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.real.test.jsx", "startTime": 1751627578923, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ForgotPasswordForm"], "duration": 445, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm renders forgot password form with all required elements", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "renders forgot password form with all required elements"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 584, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm updates email when user types", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "updates email when user types"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 167, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm shows validation error for empty email", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows validation error for empty email"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 1429, "failureDetails": [{"name": "TestingLibraryElementError"}], "failureMessages": ["Error: Unable to find an element with the text: Please enter a valid email address. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<form\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"MuiBox-root css-8atqhb\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<h1\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-h5 MuiTypography-gutterBottom css-1arqwep-MuiTypography-root\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mForgot Password\u001b[0m\n      \u001b[36m</h1>\u001b[39m\n      \u001b[36m<p\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-body2 css-1ctp1sh-MuiTypography-root\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mEnter your email address and we'll send you a link to reset your password\u001b[0m\n      \u001b[36m</p>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<label\u001b[39m\n          \u001b[33mfor\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mEmail Address\u001b[0m\n        \u001b[36m</label>\u001b[39m\n        \u001b[36m<input\u001b[39m\n          \u001b[33mid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n          \u001b[33mvalue\u001b[39m=\u001b[32m\"invalid-email\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<button\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth Mui-focusVisible MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth css-1q6jcmk-MuiButtonBase-root-MuiButton-root\"\u001b[39m\n        \u001b[33mtabindex\u001b[39m=\u001b[32m\"0\"\u001b[39m\n        \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mSend Reset Link\u001b[0m\n        \u001b[36m<span\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTouchRipple-root css-r3djoj-MuiTouchRipple-root\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<span\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"css-y4cjyz-MuiTouchRipple-ripple MuiTouchRipple-ripple MuiTouchRipple-rippleVisible MuiTouchRipple-ripplePulsate\"\u001b[39m\n            \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 1px; height: 1px; top: -0.5px; left: -0.5px;\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTouchRipple-child MuiTouchRipple-childPulsate\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</span>\u001b[39m\n        \u001b[36m</span>\u001b[39m\n      \u001b[36m</button>\u001b[39m\n      \u001b[36m<p\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-body2 css-rplv9r-MuiTypography-root\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mRemember your password?\u001b[0m\n        \u001b[0m \u001b[0m\n        \u001b[36m<button\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineAlways MuiLink-button css-779s5y-MuiTypography-root-MuiLink-root\"\u001b[39m\n          \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[0mBack to sign in\u001b[0m\n        \u001b[36m</button>\u001b[39m\n      \u001b[36m</p>\u001b[39m\n    \u001b[36m</form>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at waitForWrapper (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:163:27)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:86:33\n    at findByText (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\ForgotPasswordForm.test.jsx:77:25)\n    at Generator.call (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\ForgotPasswordForm.test.jsx:2:1)\n    at Generator._invoke [as next] (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\ForgotPasswordForm.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\ForgotPasswordForm.test.jsx:2:1)\n    at asyncGeneratorStep (C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\ForgotPasswordForm.test.jsx:2:1)"], "fullName": "ForgotPasswordForm shows validation error for invalid email", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "shows validation error for invalid email"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 184, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm clears error when user starts typing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "clears error when user starts typing"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 497, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm handles successful password reset request", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handles successful password reset request"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 475, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm handles API errors gracefully and still shows success message", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handles API errors gracefully and still shows success message"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 716, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm allows user to try again after email sent", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "allows user to try again after email sent"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 161, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm calls onSwitchToLogin when back to sign in is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onSwitchToLogin when back to sign in is clicked"}, {"ancestorTitles": ["ForgotPasswordForm"], "duration": 714, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPasswordForm calls onSwitchToLogin from email sent view", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "calls onSwitchToLogin from email sent view"}], "endTime": 1751627592729, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mForgotPasswordForm › shows validation error for invalid email\u001b[39m\u001b[22m\n\n    Unable to find an element with the text: Please enter a valid email address. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\n    Ignored nodes: comments, script, style\n    \u001b[36m<body>\u001b[39m\n      \u001b[36m<div>\u001b[39m\n        \u001b[36m<form\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"MuiBox-root css-8atqhb\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h1\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-h5 MuiTypography-gutterBottom css-1arqwep-MuiTypography-root\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mForgot Password\u001b[0m\n          \u001b[36m</h1>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-body2 css-1ctp1sh-MuiTypography-root\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mEnter your email address and we'll send you a link to reset your password\u001b[0m\n          \u001b[36m</p>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<label\u001b[39m\n              \u001b[33mfor\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mEmail Address\u001b[0m\n            \u001b[36m</label>\u001b[39m\n            \u001b[36m<input\u001b[39m\n              \u001b[33mid\u001b[39m=\u001b[32m\"input-email-address\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"email\"\u001b[39m\n              \u001b[33mvalue\u001b[39m=\u001b[32m\"invalid-email\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth Mui-focusVisible MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth css-1q6jcmk-MuiButtonBase-root-MuiButton-root\"\u001b[39m\n            \u001b[33mtabindex\u001b[39m=\u001b[32m\"0\"\u001b[39m\n            \u001b[33mtype\u001b[39m=\u001b[32m\"submit\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mSend Reset Link\u001b[0m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTouchRipple-root css-r3djoj-MuiTouchRipple-root\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<span\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"css-y4cjyz-MuiTouchRipple-ripple MuiTouchRipple-ripple MuiTouchRipple-rippleVisible MuiTouchRipple-ripplePulsate\"\u001b[39m\n                \u001b[33mstyle\u001b[39m=\u001b[32m\"width: 1px; height: 1px; top: -0.5px; left: -0.5px;\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<span\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTouchRipple-child MuiTouchRipple-childPulsate\"\u001b[39m\n                \u001b[36m/>\u001b[39m\n              \u001b[36m</span>\u001b[39m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</button>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-body2 css-rplv9r-MuiTypography-root\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mRemember your password?\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<button\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineAlways MuiLink-button css-779s5y-MuiTypography-root-MuiLink-root\"\u001b[39m\n              \u001b[33mtype\u001b[39m=\u001b[32m\"button\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mBack to sign in\u001b[0m\n            \u001b[36m</button>\u001b[39m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</form>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</body>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 75 |\u001b[39m     \u001b[36mconst\u001b[39m submitButton \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[35m/send reset link/i\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 76 |\u001b[39m     \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mclick(submitButton)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 77 |\u001b[39m     expect(\u001b[36mawait\u001b[39m screen\u001b[33m.\u001b[39mfindByText(\u001b[32m'Please enter a valid email address'\u001b[39m))\u001b[33m.\u001b[39mtoBeInTheDocument()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 78 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 79 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 80 |\u001b[39m   it(\u001b[32m'clears error when user starts typing'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat waitForWrapper (\u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/wait-for.js\u001b[2m:163:27)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/dom/dist/query-helpers.js\u001b[2m:86:33\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat findByText (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/ForgotPasswordForm.test.jsx\u001b[39m\u001b[0m\u001b[2m:77:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.call (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/ForgotPasswordForm.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator._invoke [as next] (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/ForgotPasswordForm.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/ForgotPasswordForm.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/tests/unit/components/ForgotPasswordForm.test.jsx\u001b[39m\u001b[0m\u001b[2m:2:1)\u001b[22m\u001b[2m\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\ForgotPasswordForm.test.jsx", "startTime": 1751627578913, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 108, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation renders login form by default", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders login form by default"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation renders login form when initialView is login", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders login form when initialView is login"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation renders signup form when initialView is signup", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders signup form when initialView is signup"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation renders forgot password form when initialView is forgot-password", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders forgot password form when initial<PERSON><PERSON><PERSON> is forgot-password"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation renders user dashboard when user is logged in", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders user dashboard when user is logged in"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 121, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation navigates to signup when switch to signup is clicked from login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "navigates to signup when switch to signup is clicked from login"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 79, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation navigates to forgot password when switch to forgot password is clicked from login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "navigates to forgot password when switch to forgot password is clicked from login"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 60, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation navigates to signin when switch to login is clicked from signup", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "navigates to signin when switch to login is clicked from signup"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 50, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation navigates to signin when switch to login is clicked from forgot password", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "navigates to signin when switch to login is clicked from forgot password"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation updates view when initialView prop changes", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "updates view when initialView prop changes"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation handles invalid initialView gracefully", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "handles invalid initial<PERSON><PERSON>w gracefully"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation maintains user dashboard when user is logged in regardless of initialView", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "maintains user dashboard when user is logged in regardless of initialView"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation passes correct navigation functions to child components", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "passes correct navigation functions to child components"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation renders with proper container structure", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with proper container structure"}, {"ancestorTitles": ["AuthContainer Real Implementation"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "AuthContainer Real Implementation handles user state changes correctly", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "handles user state changes correctly"}], "endTime": 1751627593163, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\AuthContainer.real.test.jsx", "startTime": 1751627591916, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["RootRedirect", "Loading State"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Loading State should show loading spinner while determining redirect", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should show loading spinner while determining redirect"}, {"ancestorTitles": ["RootRedirect", "Loading State"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Loading State should display loading spinner with correct styling", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should display loading spinner with correct styling"}, {"ancestorTitles": ["RootRedirect", "Loading State"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Loading State should display loading box with correct background gradient", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should display loading box with correct background gradient"}, {"ancestorTitles": ["RootRedirect", "Authentication-based Navigation"], "duration": 134, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Authentication-based Navigation should navigate to dashboard when user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should navigate to dashboard when user is authenticated"}, {"ancestorTitles": ["RootRedirect", "Authentication-based Navigation"], "duration": 162, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Authentication-based Navigation should navigate to signin when user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should navigate to signin when user is not authenticated"}, {"ancestorTitles": ["RootRedirect", "Authentication-based Navigation"], "duration": 134, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Authentication-based Navigation should navigate to dashboard when user object exists", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should navigate to dashboard when user object exists"}, {"ancestorTitles": ["RootRedirect", "Authentication-based Navigation"], "duration": 134, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Authentication-based Navigation should navigate to signin when user is undefined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should navigate to signin when user is undefined"}, {"ancestorTitles": ["RootRedirect", "Timer and Cleanup"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Timer and Cleanup should use setTimeout with 100ms delay", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should use setTimeout with 100ms delay"}, {"ancestorTitles": ["RootRedirect", "Timer and Cleanup"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Timer and Cleanup should cleanup timer on unmount", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should cleanup timer on unmount"}, {"ancestorTitles": ["RootRedirect", "useAuth Integration"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect useAuth Integration should call useAuth hook", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call useAuth hook"}, {"ancestorTitles": ["RootRedirect", "useAuth Integration"], "duration": 275, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect useAuth Integration should handle useAuth returning different user states", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle useAuth returning different user states"}, {"ancestorTitles": ["RootRedirect", "Component Lifecycle"], "duration": 250, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Component Lifecycle should re-run effect when user state changes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should re-run effect when user state changes"}, {"ancestorTitles": ["RootRedirect", "Component Lifecycle"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "RootRedirect Component Lifecycle should handle multiple rapid re-renders correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle multiple rapid re-renders correctly"}], "endTime": 1751627593720, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\integration\\RootRedirect.test.jsx", "startTime": 1751627591896, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["UserDashboard", "Component Rendering"], "duration": 76, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Component Rendering should render dashboard with user information", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render dashboard with user information"}, {"ancestorTitles": ["UserDashboard", "Component Rendering"], "duration": 52, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Component Rendering should render navigation items", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should render navigation items"}, {"ancestorTitles": ["UserDashboard", "Component Rendering"], "duration": 70, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Component Rendering should render dashboard statistics", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "status": "passed", "title": "should render dashboard statistics"}, {"ancestorTitles": ["UserDashboard", "Component Rendering"], "duration": 65, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Component Rendering should render account status cards", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should render account status cards"}, {"ancestorTitles": ["UserDashboard", "User Authentication Display"], "duration": 50, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard User Authentication Display should display Google OAuth for Google users", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should display Google OAuth for Google users"}, {"ancestorTitles": ["UserDashboard", "User Authentication Display"], "duration": 82, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard User Authentication Display should display Email & Password for regular users", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should display Email & Password for regular users"}, {"ancestorTitles": ["UserDashboard", "User Authentication Display"], "duration": 49, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard User Authentication Display should handle user without profile picture", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle user without profile picture"}, {"ancestorTitles": ["UserDashboard", "Notifications"], "duration": 26, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Notifications should display notification badge with unread count", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should display notification badge with unread count"}, {"ancestorTitles": ["UserDashboard", "Notifications"], "duration": 287, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Notifications should open notifications menu when bell icon is clicked", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should open notifications menu when bell icon is clicked"}, {"ancestorTitles": ["UserDashboard", "<PERSON><PERSON><PERSON>"], "duration": 189, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Settings Menu should open settings menu when settings icon is clicked", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should open settings menu when settings icon is clicked"}, {"ancestorTitles": ["UserDashboard", "<PERSON><PERSON><PERSON>"], "duration": 216, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Settings Menu should navigate to profile when Profile Settings is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should navigate to profile when Profile Settings is clicked"}, {"ancestorTitles": ["UserDashboard", "Logout Functionality"], "duration": 285, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Logout Functionality should handle successful logout", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should handle successful logout"}, {"ancestorTitles": ["UserDashboard", "Logout Functionality"], "duration": 179, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Logout Functionality should handle logout API failure gracefully", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle logout API failure gracefully"}, {"ancestorTitles": ["UserDashboard", "Logout Functionality"], "duration": 217, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Logout Functionality should clear localStorage on logout", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should clear localStorage on logout"}, {"ancestorTitles": ["UserDashboard", "Logout Functionality"], "duration": 184, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Logout Functionality should handle IP address fetch failure during logout", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle IP address fetch failure during logout"}, {"ancestorTitles": ["UserDashboard", "State Management"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard State Management should handle drawer toggle for mobile", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle drawer toggle for mobile"}, {"ancestorTitles": ["UserDashboard", "State Management"], "duration": 180, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard State Management should close menus when clicking menu items", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should close menus when clicking menu items"}, {"ancestorTitles": ["UserDashboard", "Environment Configuration"], "duration": 20, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Environment Configuration should use default API URL when environment variable is not set", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should use default API URL when environment variable is not set"}, {"ancestorTitles": ["UserDashboard", "Erro<PERSON>"], "duration": 26, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Error Handling should handle missing user gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle missing user gracefully"}, {"ancestorTitles": ["UserDashboard", "Erro<PERSON>"], "duration": 22, "failureDetails": [], "failureMessages": [], "fullName": "UserDashboard Error Handling should handle user without name gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle user without name gracefully"}], "endTime": 1751627593803, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\UserDashboard.test.jsx", "startTime": 1751627590799, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["SignUpForm"], "duration": 265, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm renders sign up form with all required fields", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "status": "passed", "title": "renders sign up form with all required fields"}, {"ancestorTitles": ["SignUpForm"], "duration": 818, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm updates form data when user types in fields", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "updates form data when user types in fields"}, {"ancestorTitles": ["SignUpForm"], "duration": 81, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm shows validation errors for empty required fields", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "shows validation errors for empty required fields"}, {"ancestorTitles": ["SignUpForm"], "duration": 194, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm shows validation error for short name", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows validation error for short name"}, {"ancestorTitles": ["SignUpForm"], "duration": 159, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm shows validation error for invalid mobile number", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows validation error for invalid mobile number"}, {"ancestorTitles": ["SignUpForm"], "duration": 272, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm clears field errors when user starts typing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "clears field errors when user starts typing"}, {"ancestorTitles": ["SignUpForm"], "duration": 870, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm handles successful registration", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handles successful registration"}, {"ancestorTitles": ["SignUpForm"], "duration": 883, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm handles registration failure with 409 status (email already exists)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles registration failure with 409 status (email already exists)"}, {"ancestorTitles": ["SignUpForm"], "duration": 1108, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm handles registration failure with other status codes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles registration failure with other status codes"}, {"ancestorTitles": ["SignUpForm"], "duration": 880, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm handles network errors during registration", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles network errors during registration"}, {"ancestorTitles": ["SignUpForm"], "duration": 160, "failureDetails": [], "failureMessages": [], "fullName": "SignUpForm toggles terms and conditions checkbox", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "toggles terms and conditions checkbox"}], "endTime": 1751627593847, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\SignUpForm.test.tsx", "startTime": 1751627587181, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["NotFound Page", "Component Rendering"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Component Rendering should render 404 error message", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render 404 error message"}, {"ancestorTitles": ["NotFound Page", "Component Rendering"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Component Rendering should render page not found message", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render page not found message"}, {"ancestorTitles": ["NotFound Page", "Component Rendering"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Component Rendering should render return to home link", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render return to home link"}, {"ancestorTitles": ["NotFound Page", "Component Rendering"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Component Rendering should render with correct CSS classes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render with correct CSS classes"}, {"ancestorTitles": ["NotFound Page", "Typography and Styling"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Typography and Styling should render 404 heading with correct styling", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render 404 heading with correct styling"}, {"ancestorTitles": ["NotFound Page", "Typography and Styling"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Typography and Styling should render description with correct styling", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render description with correct styling"}, {"ancestorTitles": ["NotFound Page", "Typography and Styling"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Typography and Styling should render home link with correct styling", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render home link with correct styling"}, {"ancestorTitles": ["NotFound Page", "Navigation Functionality"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Navigation Functionality should have correct href attribute on home link", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should have correct href attribute on home link"}, {"ancestorTitles": ["NotFound Page", "Navigation Functionality"], "duration": 70, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Navigation Functionality should be clickable home link", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should be clickable home link"}, {"ancestorTitles": ["NotFound Page", "Accessibility"], "duration": 37, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Accessibility should have proper heading hierarchy", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should have proper heading hierarchy"}, {"ancestorTitles": ["NotFound Page", "Accessibility"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Accessibility should have accessible link", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should have accessible link"}, {"ancestorTitles": ["NotFound Page", "Accessibility"], "duration": 43, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Accessibility should be keyboard navigable", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be keyboard navigable"}, {"ancestorTitles": ["NotFound Page", "Content Verification"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Content Verification should display correct error code", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should display correct error code"}, {"ancestorTitles": ["NotFound Page", "Content Verification"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Content Verification should display user-friendly error message", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should display user-friendly error message"}, {"ancestorTitles": ["NotFound Page", "Content Verification"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Content Verification should provide clear navigation option", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should provide clear navigation option"}, {"ancestorTitles": ["NotFound Page", "Component Structure"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Component Structure should render complete component structure", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should render complete component structure"}, {"ancestorTitles": ["NotFound Page", "Component Structure"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Component Structure should have proper nesting structure", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have proper nesting structure"}, {"ancestorTitles": ["NotFound Page", "Erro<PERSON>"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page <PERSON><PERSON> should render without crashing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render without crashing"}, {"ancestorTitles": ["NotFound Page", "Erro<PERSON>"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Error Handling should handle multiple renders", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle multiple renders"}, {"ancestorTitles": ["NotFound Page", "Erro<PERSON>"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page <PERSON>rror <PERSON> should handle unmounting gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle unmounting gracefully"}, {"ancestorTitles": ["NotFound Page", "Responsive Design"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Responsive Design should use responsive classes for layout", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should use responsive classes for layout"}, {"ancestorTitles": ["NotFound Page", "Responsive Design"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Responsive Design should center content properly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should center content properly"}, {"ancestorTitles": ["NotFound Page", "Visual Design"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Visual Design should use appropriate background color", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should use appropriate background color"}, {"ancestorTitles": ["NotFound Page", "Visual Design"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Visual Design should use appropriate text colors", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should use appropriate text colors"}, {"ancestorTitles": ["NotFound Page", "Visual Design"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NotFound Page Visual Design should include hover effects on link", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should include hover effects on link"}], "endTime": 1751627593931, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\NotFound.test.tsx", "startTime": 1751627593240, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AuthContext Functionality"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality provides initial auth state", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "provides initial auth state"}, {"ancestorTitles": ["AuthContext Functionality"], "duration": 22, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality handles successful login", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "handles successful login"}, {"ancestorTitles": ["AuthContext Functionality"], "duration": 129, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality handles login failure", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handles login failure"}, {"ancestorTitles": ["AuthContext Functionality"], "duration": 148, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality handles sign up", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handles sign up"}, {"ancestorTitles": ["AuthContext Functionality"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality handles logout", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "handles logout"}, {"ancestorTitles": ["AuthContext Functionality"], "duration": 147, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality handles forgot password", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles forgot password"}, {"ancestorTitles": ["AuthContext Functionality"], "duration": 158, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality handles reset password", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles reset password"}, {"ancestorTitles": ["AuthContext Functionality"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Functionality sets authenticated user", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "sets authenticated user"}], "endTime": 1751627593948, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\contexts\\AuthContext.simple.test.tsx", "startTime": 1751627592797, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["SignUp Page", "Authentication Redirect"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Authentication Redirect should redirect to dashboard when user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should redirect to dashboard when user is authenticated"}, {"ancestorTitles": ["SignUp Page", "Authentication Redirect"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Authentication Redirect should not redirect when user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not redirect when user is not authenticated"}, {"ancestorTitles": ["SignUp Page", "Authentication Redirect"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Authentication Redirect should not render form when user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not render form when user is authenticated"}, {"ancestorTitles": ["SignUp Page", "Component Rendering"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Component Rendering should render ThemeProvider with correct theme configuration", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should render ThemeProvider with correct theme configuration"}, {"ancestorTitles": ["SignUp Page", "Component Rendering"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Component Rendering should render CssBaseline component", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render CssBaseline component"}, {"ancestorTitles": ["SignUp Page", "Component Rendering"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Component Rendering should render AuthContainer with signup initial view", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render AuthContainer with signup initial view"}, {"ancestorTitles": ["SignUp Page", "Component Rendering"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Component Rendering should render complete component structure when not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render complete component structure when not authenticated"}, {"ancestorTitles": ["SignUp Page", "useAuth Integration"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page useAuth Integration should call useAuth hook", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call useAuth hook"}, {"ancestorTitles": ["SignUp Page", "useAuth Integration"], "duration": 24, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page useAuth Integration should handle different user states", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle different user states"}, {"ancestorTitles": ["SignUp Page", "useAuth Integration"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page useAuth Integration should handle undefined user", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle undefined user"}, {"ancestorTitles": ["SignUp Page", "Navigation Integration"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Navigation Integration should use navigate hook correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should use navigate hook correctly"}, {"ancestorTitles": ["SignUp Page", "Navigation Integration"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Navigation Integration should handle navigation with different user objects", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle navigation with different user objects"}, {"ancestorTitles": ["SignUp Page", "Component Lifecycle"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Component Lifecycle should handle component mount and unmount", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle component mount and unmount"}, {"ancestorTitles": ["SignUp Page", "Component Lifecycle"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Component Lifecycle should re-run effect when user state changes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should re-run effect when user state changes"}, {"ancestorTitles": ["SignUp Page", "Erro<PERSON>"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Error Handling should handle useAuth returning null context", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle useAuth returning null context"}, {"ancestorTitles": ["SignUp Page", "Erro<PERSON>"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Error Handling should handle missing user property in auth context", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle missing user property in auth context"}, {"ancestorTitles": ["SignUp Page", "Erro<PERSON>"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Error Handling should handle useAuth returning undefined", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle useAuth returning undefined"}, {"ancestorTitles": ["SignUp Page", "Theme Configuration"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page Theme Configuration should use consistent theme with SignIn page", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should use consistent theme with SignIn page"}, {"ancestorTitles": ["SignUp Page", "AuthContainer Integration"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page AuthContainer Integration should pass correct initialView prop to AuthContainer", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should pass correct initialV<PERSON>w prop to <PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ancestorTitles": ["SignUp Page", "AuthContainer Integration"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "SignUp Page AuthContainer Integration should render AuthContainer when user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render AuthContainer when user is not authenticated"}], "endTime": 1751627594530, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\SignUp.test.jsx", "startTime": 1751627593794, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useToast", "Initial State"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "useToast Initial State should return empty toasts array initially", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return empty toasts array initially"}, {"ancestorTitles": ["useToast", "Initial State"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "useToast Initial State should provide toast function", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should provide toast function"}, {"ancestorTitles": ["useToast", "Initial State"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "useToast Initial State should provide dismiss function", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should provide dismiss function"}, {"ancestorTitles": ["useToast", "Toast Creation"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Creation should create a toast with basic properties", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should create a toast with basic properties"}, {"ancestorTitles": ["useToast", "Toast Creation"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Creation should create toast with unique IDs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should create toast with unique IDs"}, {"ancestorTitles": ["useToast", "Toast Creation"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Creation should respect TOAST_LIMIT", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should respect TOAST_LIMIT"}, {"ancestorTitles": ["useToast", "Toast Creation"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Creation should create toast with onOpenChange handler", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should create toast with onOpenChange handler"}, {"ancestorTitles": ["useToast", "Toast Dismissal"], "duration": 51, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Dismissal should dismiss specific toast by ID", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should dismiss specific toast by ID"}, {"ancestorTitles": ["useToast", "Toast Dismissal"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Dismissal should dismiss all toasts when no ID provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should dismiss all toasts when no ID provided"}, {"ancestorTitles": ["useToast", "Toast Dismissal"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Dismissal should call onOpenChange when dismissing via onOpenChange", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call on<PERSON>pen<PERSON><PERSON><PERSON> when dismissing via onOpenChange"}, {"ancestorTitles": ["useToast", "Toast Dismissal"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Dismissal should not dismiss when onOpenChange called with true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not dismiss when on<PERSON><PERSON><PERSON><PERSON><PERSON> called with true"}, {"ancestorTitles": ["useToast", "Toast Removal"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Removal should remove toast after delay", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should remove toast after delay"}, {"ancestorTitles": ["useToast", "Toast Removal"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Removal should not add duplicate timeouts for same toast", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not add duplicate timeouts for same toast"}, {"ancestorTitles": ["useToast", "Toast Updates"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Updates should update toast properties", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should update toast properties"}, {"ancestorTitles": ["useToast", "Toast Updates"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Updates should return update function from toast creation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return update function from toast creation"}, {"ancestorTitles": ["useToast", "Toast Updates"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "useToast Toast Updates should return dismiss function from toast creation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return dismiss function from toast creation"}, {"ancestorTitles": ["useToast", "Standalone Toast Function"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "useToast Standalone Toast Function should work independently of useToast hook", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should work independently of useToast hook"}, {"ancestorTitles": ["useToast", "Standalone Toast Function"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "useToast Standalone Toast Function should generate unique IDs for standalone toasts", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should generate unique IDs for standalone toasts"}, {"ancestorTitles": ["useToast", "Hook State Management"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "useToast Hook State Management should sync state across multiple hook instances", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should sync state across multiple hook instances"}, {"ancestorTitles": ["useToast", "Hook State Management"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "useToast Hook State Management should cleanup listeners on unmount", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should cleanup listeners on unmount"}, {"ancestorTitles": ["Toast Reducer", "ADD_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer ADD_TOAST should add toast to empty state", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should add toast to empty state"}, {"ancestorTitles": ["Toast Reducer", "ADD_TOAST"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer ADD_TOAST should add toast to beginning of array", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should add toast to beginning of array"}, {"ancestorTitles": ["Toast Reducer", "ADD_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer ADD_TOAST should respect TOAST_LIMIT", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should respect TOAST_LIMIT"}, {"ancestorTitles": ["Toast Reducer", "UPDATE_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer UPDATE_TOAST should update existing toast", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should update existing toast"}, {"ancestorTitles": ["Toast Reducer", "UPDATE_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer UPDATE_TOAST should not update non-existing toast", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not update non-existing toast"}, {"ancestorTitles": ["Toast Reducer", "UPDATE_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer UPDATE_TOAST should update multiple properties", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should update multiple properties"}, {"ancestorTitles": ["Toast Reducer", "DISMISS_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer DISMISS_TOAST should dismiss specific toast", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should dismiss specific toast"}, {"ancestorTitles": ["Toast Reducer", "DISMISS_TOAST"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer DISMISS_TOAST should dismiss all toasts when no ID provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should dismiss all toasts when no ID provided"}, {"ancestorTitles": ["Toast Reducer", "DISMISS_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer DISMISS_TOAST should not affect non-matching toasts", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should not affect non-matching toasts"}, {"ancestorTitles": ["Toast Reducer", "REMOVE_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer REMOVE_TOAST should remove specific toast", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should remove specific toast"}, {"ancestorTitles": ["Toast Reducer", "REMOVE_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer REMOVE_TOAST should remove all toasts when no ID provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should remove all toasts when no ID provided"}, {"ancestorTitles": ["Toast Reducer", "REMOVE_TOAST"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Toast Reducer REMOVE_TOAST should handle removing non-existing toast", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle removing non-existing toast"}], "endTime": 1751627594684, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\use-toast.test.ts", "startTime": 1751627593869, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ProtectedRoute", "Loading State"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Loading State should show loading spinner when localStorage access is delayed", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should show loading spinner when localStorage access is delayed"}, {"ancestorTitles": ["ProtectedRoute", "Loading State"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Loading State should handle component rendering lifecycle correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle component rendering lifecycle correctly"}, {"ancestorTitles": ["ProtectedRoute", "Authentication Check"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Authentication Check should redirect to signin when no authentication tokens exist", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should redirect to signin when no authentication tokens exist"}, {"ancestorTitles": ["ProtectedRoute", "Authentication Check"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Authentication Check should redirect to signin when only userId exists", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should redirect to signin when only userId exists"}, {"ancestorTitles": ["ProtectedRoute", "Authentication Check"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Authentication Check should redirect to signin when only sessionToken exists", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should redirect to signin when only sessionToken exists"}, {"ancestorTitles": ["ProtectedRoute", "Authentication Check"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Authentication Check should render children when both userId and sessionToken exist", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render children when both userId and sessionToken exist"}, {"ancestorTitles": ["ProtectedRoute", "localStorage Integration"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute localStorage Integration should call localStorage.getItem for userId and sessionToken", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should call localStorage.getItem for userId and sessionToken"}, {"ancestorTitles": ["ProtectedRoute", "localStorage Integration"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute localStorage Integration should handle localStorage errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle localStorage errors gracefully"}, {"ancestorTitles": ["ProtectedRoute", "Component Lifecycle"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Component Lifecycle should check authentication on mount", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should check authentication on mount"}, {"ancestorTitles": ["ProtectedRoute", "Component Lifecycle"], "duration": 39, "failureDetails": [], "failureMessages": [], "fullName": "ProtectedRoute Component Lifecycle should not re-check authentication on re-render with same props", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should not re-check authentication on re-render with same props"}], "endTime": 1751627594722, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\integration\\ProtectedRoute.test.jsx", "startTime": 1751627594006, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["BasePasswordInput"], "duration": 123, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput renders with required props", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with required props"}, {"ancestorTitles": ["BasePasswordInput"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput shows lock icon when showStartAdornment is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows lock icon when showStartAdornment is true"}, {"ancestorTitles": ["BasePasswordInput"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput does not show lock icon when showStartAdornment is false", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not show lock icon when showStartAdornment is false"}, {"ancestorTitles": ["BasePasswordInput"], "duration": 37, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput shows eye icon when value is present and toggles to eyeoff on click", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "shows eye icon when value is present and toggles to eyeoff on click"}, {"ancestorTitles": ["BasePasswordInput"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput does not show endAdornment when value is empty", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not show endAdornment when value is empty"}, {"ancestorTitles": ["BasePasswordInput"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput disables IconButton when disabled is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "disables Icon<PERSON>utton when disabled is true"}, {"ancestorTitles": ["BasePasswordInput"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput forwards extra props to TextField", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to TextField"}, {"ancestorTitles": ["BasePasswordInput"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "BasePasswordInput renders with helperText and error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with helperText and error"}], "endTime": 1751627594853, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\BasePasswordInput.test.jsx", "startTime": 1751627594029, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AuthContext Real Implementation"], "duration": 26, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation provides initial auth state", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "provides initial auth state"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation handles successful login", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handles successful login"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation handles login failure", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handles login failure"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation handles successful sign up", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handles successful sign up"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation handles logout", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "handles logout"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation handles forgot password", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles forgot password"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation handles reset password", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles reset password"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation sets authenticated user", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "sets authenticated user"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 183, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation throws error when useAuth is used outside AuthProvider", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "throws error when useAuth is used outside AuthProvider"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation loads existing user from localStorage on mount", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "loads existing user from localStorage on mount"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 22, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation handles Google login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles Google login"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation sets authenticated user with profilePicture", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "sets authenticated user with profilePicture"}, {"ancestorTitles": ["AuthContext Real Implementation"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "AuthContext Real Implementation loads user with profilePicture from localStorage on mount", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "loads user with profilePicture from localStorage on mount"}], "endTime": 1751627594929, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\contexts\\AuthContext.real.test.tsx", "startTime": 1751627593915, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["EmailInput"], "duration": 69, "failureDetails": [], "failureMessages": [], "fullName": "EmailInput renders with required props", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with required props"}, {"ancestorTitles": ["EmailInput"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "EmailInput shows mail icon when showStartAdornment is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows mail icon when showStartAdornment is true"}, {"ancestorTitles": ["EmailInput"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "EmailInput does not show mail icon when showStartAdornment is false", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not show mail icon when showStartAdornment is false"}, {"ancestorTitles": ["EmailInput"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "EmailInput forwards extra props to TextField", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to TextField"}, {"ancestorTitles": ["EmailInput"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "EmailInput renders with helperText and error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with helperText and error"}, {"ancestorTitles": ["EmailInput"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "EmailInput renders with custom label and placeholder", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with custom label and placeholder"}], "endTime": 1751627595381, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\EmailInput.test.jsx", "startTime": 1751627594814, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ForgotPassword Page", "Authentication Redirect"], "duration": 22, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Authentication Redirect should redirect to dashboard when user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should redirect to dashboard when user is authenticated"}, {"ancestorTitles": ["ForgotPassword Page", "Authentication Redirect"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Authentication Redirect should not redirect when user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not redirect when user is not authenticated"}, {"ancestorTitles": ["ForgotPassword Page", "Authentication Redirect"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Authentication Redirect should not render form when user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not render form when user is authenticated"}, {"ancestorTitles": ["ForgotPassword Page", "Component Rendering"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Component Rendering should render ThemeProvider with correct theme configuration", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should render ThemeProvider with correct theme configuration"}, {"ancestorTitles": ["ForgotPassword Page", "Component Rendering"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Component Rendering should render CssBaseline component", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render CssBaseline component"}, {"ancestorTitles": ["ForgotPassword Page", "Component Rendering"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Component Rendering should render AuthContainer with forgot-password initial view", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render AuthContainer with forgot-password initial view"}, {"ancestorTitles": ["ForgotPassword Page", "Component Rendering"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Component Rendering should render complete component structure when not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should render complete component structure when not authenticated"}, {"ancestorTitles": ["ForgotPassword Page", "useAuth Integration"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page useAuth Integration should call useAuth hook", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call useAuth hook"}, {"ancestorTitles": ["ForgotPassword Page", "useAuth Integration"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page useAuth Integration should handle different user states", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle different user states"}, {"ancestorTitles": ["ForgotPassword Page", "useAuth Integration"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page useAuth Integration should handle undefined user", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle undefined user"}, {"ancestorTitles": ["ForgotPassword Page", "Navigation Integration"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Navigation Integration should use navigate hook correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should use navigate hook correctly"}, {"ancestorTitles": ["ForgotPassword Page", "Navigation Integration"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Navigation Integration should handle navigation with different user objects", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle navigation with different user objects"}, {"ancestorTitles": ["ForgotPassword Page", "Component Lifecycle"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Component Lifecycle should handle component mount and unmount", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle component mount and unmount"}, {"ancestorTitles": ["ForgotPassword Page", "Component Lifecycle"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Component Lifecycle should re-run effect when user state changes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should re-run effect when user state changes"}, {"ancestorTitles": ["ForgotPassword Page", "Erro<PERSON>"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Error Handling should handle useAuth returning null context", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle useAuth returning null context"}, {"ancestorTitles": ["ForgotPassword Page", "Erro<PERSON>"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Error Handling should handle missing user property in auth context", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle missing user property in auth context"}, {"ancestorTitles": ["ForgotPassword Page", "Erro<PERSON>"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Error Handling should handle useAuth returning undefined", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle useAuth returning undefined"}, {"ancestorTitles": ["ForgotPassword Page", "Theme Configuration"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Theme Configuration should use consistent theme with other auth pages", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should use consistent theme with other auth pages"}, {"ancestorTitles": ["ForgotPassword Page", "AuthContainer Integration"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page AuthContainer Integration should pass correct initialView prop to AuthContainer", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should pass correct initialV<PERSON>w prop to <PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ancestorTitles": ["ForgotPassword Page", "AuthContainer Integration"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page AuthContainer Integration should render AuthContainer when user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render AuthContainer when user is not authenticated"}, {"ancestorTitles": ["ForgotPassword Page", "Forgot Password Specific Features"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Forgot Password Specific Features should render forgot password view specifically", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render forgot password view specifically"}, {"ancestorTitles": ["ForgotPassword Page", "Forgot Password Specific Features"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "ForgotPassword Page Forgot Password Specific Features should maintain forgot password context throughout component lifecycle", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should maintain forgot password context throughout component lifecycle"}], "endTime": 1751627595402, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\ForgotPassword.test.jsx", "startTime": 1751627594596, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["FormHeader"], "duration": 61, "failureDetails": [], "failureMessages": [], "fullName": "FormHeader renders title and subtitle when provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders title and subtitle when provided"}, {"ancestorTitles": ["FormHeader"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "FormHeader renders only title if subtitle is not provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders only title if subtitle is not provided"}, {"ancestorTitles": ["FormHeader"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "FormHeader renders only subtitle if title is not provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders only subtitle if title is not provided"}, {"ancestorTitles": ["FormHeader"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "FormHeader applies titleSx and subtitleSx props", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "applies titleSx and subtitleSx props"}, {"ancestorTitles": ["FormHeader"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "<PERSON><PERSON><PERSON><PERSON> forwards extra props to <PERSON><PERSON><PERSON> for title", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to <PERSON><PERSON><PERSON> for title"}], "endTime": 1751627595416, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\FormHeader.test.jsx", "startTime": 1751627594780, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["LoginForm"], "duration": 186, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm renders login form with all required fields", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "renders login form with all required fields"}, {"ancestorTitles": ["LoginForm"], "duration": 830, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm updates form data when user types in fields", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "updates form data when user types in fields"}, {"ancestorTitles": ["LoginForm"], "duration": 195, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm shows validation errors for empty fields", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "shows validation errors for empty fields"}, {"ancestorTitles": ["LoginForm"], "duration": 279, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm shows validation error for short password", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows validation error for short password"}, {"ancestorTitles": ["LoginForm"], "duration": 742, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm calls login function with correct credentials on valid form submission", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls login function with correct credentials on valid form submission"}, {"ancestorTitles": ["LoginForm"], "duration": 707, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm shows success toast on successful login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows success toast on successful login"}, {"ancestorTitles": ["LoginForm"], "duration": 844, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm shows error toast on failed login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows error toast on failed login"}, {"ancestorTitles": ["LoginForm"], "duration": 498, "failureDetails": [], "failureMessages": [], "fullName": "LoginForm clears field errors when user starts typing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "clears field errors when user starts typing"}], "endTime": 1751627595604, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\LoginForm.test.jsx", "startTime": 1751627590528, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 90, "failureDetails": [], "failureMessages": [], "fullName": "AuthFormFooter renders message and linkText", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders message and linkText"}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "<PERSON><PERSON>Form<PERSON>oot<PERSON> calls onLinkClick when link is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onLinkClick when link is clicked"}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "AuthFormFooter renders with empty message and linkText", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with empty message and linkText"}], "endTime": 1751627595638, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\AuthFormFooter.test.jsx", "startTime": 1751627594951, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AuthForm<PERSON><PERSON><PERSON>"], "duration": 87, "failureDetails": [], "failureMessages": [], "fullName": "AuthFormHeader renders logo, title, subtitle, Google login, and divider by default", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "renders logo, title, subtitle, Google login, and divider by default"}, {"ancestorTitles": ["AuthForm<PERSON><PERSON><PERSON>"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "AuthFormHeader does not render logo if showLogo is false", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not render logo if show<PERSON><PERSON> is false"}, {"ancestorTitles": ["AuthForm<PERSON><PERSON><PERSON>"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "AuthFormHeader does not render Google login or divider if showGoogleLogin is false", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "does not render Google login or divider if showGoogleLogin is false"}, {"ancestorTitles": ["AuthForm<PERSON><PERSON><PERSON>"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "AuthFormHeader renders custom logo src and alt", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders custom logo src and alt"}, {"ancestorTitles": ["AuthForm<PERSON><PERSON><PERSON>"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "AuthFormHeader renders with empty title and subtitle", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders with empty title and subtitle"}], "endTime": 1751627595694, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\AuthFormHeader.test.jsx", "startTime": 1751627594992, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["TextInput"], "duration": 51, "failureDetails": [], "failureMessages": [], "fullName": "TextInput renders with required props", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with required props"}, {"ancestorTitles": ["TextInput"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "TextInput shows startAdornment when provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows startAdornment when provided"}, {"ancestorTitles": ["TextInput"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "TextInput shows endAdornment when provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows endAdornment when provided"}, {"ancestorTitles": ["TextInput"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "TextInput does not show adornments when not provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "does not show adornments when not provided"}, {"ancestorTitles": ["TextInput"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "TextInput forwards extra props to TextField", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to TextField"}, {"ancestorTitles": ["TextInput"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "TextInput renders with helperText and error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with helperText and error"}, {"ancestorTitles": ["TextInput"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "TextInput renders with custom label, type, and placeholder", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders with custom label, type, and placeholder"}], "endTime": 1751627595945, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\TextInput.test.jsx", "startTime": 1751627595454, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["FormContainer"], "duration": 71, "failureDetails": [], "failureMessages": [], "fullName": "FormContainer renders children inside Paper and Box", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders children inside Paper and Box"}, {"ancestorTitles": ["FormContainer"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "FormContainer renders logo when showLogo is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders logo when showLogo is true"}, {"ancestorTitles": ["FormContainer"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "FormContainer does not render logo when showLogo is false", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not render logo when show<PERSON>ogo is false"}, {"ancestorTitles": ["FormContainer"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "FormContainer applies custom maxWidth and sx", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "applies custom maxWidth and sx"}, {"ancestorTitles": ["FormContainer"], "duration": 21, "failureDetails": [], "failureMessages": [], "fullName": "FormContainer forwards extra props to <PERSON>", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to <PERSON>"}], "endTime": 1751627596071, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\FormContainer.test.jsx", "startTime": 1751627595481, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["SubmitButton"], "duration": 183, "failureDetails": [], "failureMessages": [], "fullName": "SubmitButton renders children by default", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders children by default"}, {"ancestorTitles": ["SubmitButton"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SubmitButton renders CircularProgress when loading", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders CircularProgress when loading"}, {"ancestorTitles": ["SubmitButton"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "SubmitButton disables button when disabled or loading", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "disables button when disabled or loading"}, {"ancestorTitles": ["SubmitButton"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SubmitButton applies variant, size, type, and fullWidth props", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "applies variant, size, type, and fullWidth props"}, {"ancestorTitles": ["SubmitButton"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "SubmitButton merges custom sx and applies authStyle gradient", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "merges custom sx and applies authStyle gradient"}, {"ancestorTitles": ["SubmitButton"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "SubmitButton forwards extra props to Button", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to <PERSON><PERSON>"}], "endTime": 1751627596211, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\SubmitButton.test.jsx", "startTime": 1751627595463, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["PasswordInput"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "PasswordInput renders BasePasswordInput with default autoComplete", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders BasePasswordInput with default autoComplete"}, {"ancestorTitles": ["PasswordInput"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "PasswordInput forwards all props to BasePasswordInput", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards all props to BasePasswordInput"}, {"ancestorTitles": ["PasswordInput"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "PasswordInput overrides default autoComplete if provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "overrides default autoComplete if provided"}], "endTime": 1751627596383, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\PasswordInput.test.jsx", "startTime": 1751627595731, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["PasswordFormSection"], "duration": 92, "failureDetails": [], "failureMessages": [], "fullName": "PasswordFormSection renders all child components with correct props", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders all child components with correct props"}, {"ancestorTitles": ["PasswordFormSection"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "PasswordFormSection calls onSubmit when form is submitted", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onSubmit when form is submitted"}, {"ancestorTitles": ["PasswordFormSection"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "PasswordFormSection shows AlertMessage when apiError is present", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "shows Al<PERSON><PERSON><PERSON>age when a<PERSON><PERSON><PERSON><PERSON> is present"}, {"ancestorTitles": ["PasswordFormSection"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "PasswordFormSection disables child components when disabled is true", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "disables child components when disabled is true"}, {"ancestorTitles": ["PasswordFormSection"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "PasswordFormSection passes validation errors to child components", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "passes validation errors to child components"}], "endTime": 1751627596451, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\PasswordFormSection.test.jsx", "startTime": 1751627595696, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["usePasswordValidation"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "usePasswordValidation returns required error if password is empty", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "returns required error if password is empty"}, {"ancestorTitles": ["usePasswordValidation"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "usePasswordValidation returns rule errors for invalid password", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "returns rule errors for invalid password"}, {"ancestorTitles": ["usePasswordValidation"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "usePasswordValidation returns no errors for valid password", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "returns no errors for valid password"}, {"ancestorTitles": ["usePasswordValidation"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "usePasswordValidation returns confirm required if confirmPassword is empty and touched", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "returns confirm required if confirm<PERSON>ass<PERSON> is empty and touched"}, {"ancestorTitles": ["usePasswordValidation"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "usePasswordValidation returns mismatch if passwords do not match", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "returns mismatch if passwords do not match"}, {"ancestorTitles": ["usePasswordValidation"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "usePasswordValidation returns passwordsMatch true if passwords match", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "returns passwordsMatch true if passwords match"}, {"ancestorTitles": ["usePasswordValidation"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "usePasswordValidation isValid is false if password is invalid or passwords do not match", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "isValid is false if password is invalid or passwords do not match"}], "endTime": 1751627596470, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\usePasswordValidation.test.js", "startTime": 1751627595757, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useDeviceInfo"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "useDeviceInfo fetches IP address from API and caches it", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "fetches IP address from API and caches it"}, {"ancestorTitles": ["useDeviceInfo"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "useDeviceInfo returns empty string on fetch error", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "returns empty string on fetch error"}, {"ancestorTitles": ["useDeviceInfo"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "useDeviceInfo returns device details from navigator.userAgent", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "returns device details from navigator.userAgent"}, {"ancestorTitles": ["useDeviceInfo"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "useDeviceInfo getDeviceInfo returns both IP and device details", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "getDeviceInfo returns both IP and device details"}], "endTime": 1751627596754, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\hooks\\useDeviceInfo.test.js", "startTime": 1751627596150, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["TermsCheckbox"], "duration": 64, "failureDetails": [], "failureMessages": [], "fullName": "TermsCheckbox renders checkbox and label", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders checkbox and label"}, {"ancestorTitles": ["TermsCheckbox"], "duration": 20, "failureDetails": [], "failureMessages": [], "fullName": "TermsCheckbox calls onChange when checkbox is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls on<PERSON><PERSON><PERSON> when checkbox is clicked"}, {"ancestorTitles": ["TermsCheckbox"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "TermsCheckbox disables checkbox when disabled is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "disables checkbox when disabled is true"}, {"ancestorTitles": ["TermsCheckbox"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "TermsCheckbox shows error color when error is true", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "shows error color when error is true"}, {"ancestorTitles": ["TermsCheckbox"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "TermsCheckbox renders custom terms and privacy URLs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders custom terms and privacy URLs"}, {"ancestorTitles": ["TermsCheckbox"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "TermsCheckbox forwards extra props to FormControlLabel", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to FormControlLabel"}], "endTime": 1751627596790, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\TermsCheckbox.test.jsx", "startTime": 1751627596099, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["OAuth2Redirect"], "duration": 111, "failureDetails": [], "failureMessages": [], "fullName": "OAuth2Redirect redirects to /signin if any token is missing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "redirects to /signin if any token is missing"}, {"ancestorTitles": ["OAuth2Redirect"], "duration": 86, "failureDetails": [], "failureMessages": [], "fullName": "OAuth2Redirect always renders spinner", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "always renders spinner"}], "endTime": 1751627596790, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\OAuth2Redirect.test.jsx", "startTime": 1751627592166, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["SignIn Page"], "duration": 21, "failureDetails": [], "failureMessages": [], "fullName": "SignIn Page renders AuthContainer with initialView=\"login\" when not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders AuthContainer with initialView=\"login\" when not authenticated"}, {"ancestorTitles": ["SignIn Page"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "SignIn Page redirects to /dashboard if user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "redirects to /dashboard if user is authenticated"}, {"ancestorTitles": ["SignIn Page"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "SignIn Page does not render AuthContainer if user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not render AuthContainer if user is authenticated"}, {"ancestorTitles": ["SignIn Page"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "SignIn Page renders nothing if user is authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders nothing if user is authenticated"}], "endTime": 1751627596880, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\pages\\SignIn.test.jsx", "startTime": 1751627596279, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["cn utility function"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function combines class names correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "combines class names correctly"}, {"ancestorTitles": ["cn utility function"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function handles conditional classes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles conditional classes"}, {"ancestorTitles": ["cn utility function"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function merges Tailwind classes correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "merges Tailwind classes correctly"}, {"ancestorTitles": ["cn utility function"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function handles arrays of classes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles arrays of classes"}, {"ancestorTitles": ["cn utility function"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function handles objects with boolean values", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles objects with boolean values"}, {"ancestorTitles": ["cn utility function"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function handles undefined and null values", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles undefined and null values"}, {"ancestorTitles": ["cn utility function"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function handles empty input", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles empty input"}, {"ancestorTitles": ["cn utility function"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "cn utility function handles complex mixed inputs", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles complex mixed inputs"}], "endTime": 1751627597018, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\lib\\utils.test.ts", "startTime": 1751627596517, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["<PERSON><PERSON>"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Jest <PERSON>up should run basic tests", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should run basic tests"}, {"ancestorTitles": ["<PERSON><PERSON>"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "Jest Setup should have access to Jest globals", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have access to Jest globals"}, {"ancestorTitles": ["<PERSON><PERSON>"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "<PERSON><PERSON> should have jsdom environment", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have jsdom environment"}, {"ancestorTitles": ["<PERSON><PERSON>"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Jest <PERSON>up should have testing library matchers", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should have testing library matchers"}, {"ancestorTitles": ["<PERSON><PERSON>"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Jest <PERSON>up should mock localStorage", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should mock localStorage"}, {"ancestorTitles": ["<PERSON><PERSON>"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Jest Setup should mock matchMedia", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should mock matchMedia"}], "endTime": 1751627597054, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\setup.test.js", "startTime": 1751627596566, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AlertM<PERSON>age"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "AlertMessage renders nothing if show is false", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders nothing if show is false"}, {"ancestorTitles": ["AlertM<PERSON>age"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "AlertMessage renders nothing if message is empty", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders nothing if message is empty"}, {"ancestorTitles": ["AlertM<PERSON>age"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "AlertMessage renders with default severity and message", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with default severity and message"}, {"ancestorTitles": ["AlertM<PERSON>age"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "AlertMessage renders with custom severity", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with custom severity"}, {"ancestorTitles": ["AlertM<PERSON>age"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "AlertMessage applies custom sx prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies custom sx prop"}, {"ancestorTitles": ["AlertM<PERSON>age"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "<PERSON>ertMessage forwards extra props to <PERSON><PERSON>", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards extra props to <PERSON><PERSON>"}], "endTime": 1751627597053, "message": "", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\common\\AlertMessage.test.jsx", "startTime": 1751627596447, "status": "passed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1751627601913, "message": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[31m\u001b[1mConfiguration error\u001b[22m:\u001b[39m\n    \u001b[31m\u001b[39m\n    \u001b[31mCould not locate module \u001b[1m@/tests/unit/ForgotPasswordForm\u001b[22m mapped as:\u001b[39m\n    \u001b[31m\u001b[1mC:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\$1\u001b[22m.\u001b[39m\n    \u001b[31m\u001b[39m\n    \u001b[31mPlease check your configuration for these entries:\u001b[39m\n    \u001b[31m{\u001b[39m\n    \u001b[31m  \"moduleNameMapper\": {\u001b[39m\n    \u001b[31m    \"/^@\\/(.*)$/\": \"\u001b[1mC:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\$1\u001b[22m\"\u001b[39m\n    \u001b[31m  },\u001b[39m\n    \u001b[31m  \"resolver\": \u001b[1mundefined\u001b[22m\u001b[39m\n    \u001b[31m}\u001b[39m\n\n    \u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m// });\u001b[39m\n     \u001b[90m 48 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m jest\u001b[33m.\u001b[39mmock(\u001b[32m'@/tests/unit/ForgotPasswordForm'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n     \u001b[90m    |\u001b[39m      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 50 |\u001b[39m   \u001b[36mreturn\u001b[39m \u001b[36mfunction\u001b[39m \u001b[33mMockForgotPasswordForm\u001b[39m({ onSwitchToLogin }) {\n     \u001b[90m 51 |\u001b[39m     \u001b[36mreturn\u001b[39m (\n     \u001b[90m 52 |\u001b[39m       \u001b[33m<\u001b[39m\u001b[33mdiv\u001b[39m data\u001b[33m-\u001b[39mtestid\u001b[33m=\u001b[39m\u001b[32m\"forgot-password-form\"\u001b[39m\u001b[33m>\u001b[39m\u001b[0m\n\n      \u001b[2mat createNoMappedModuleFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:759:17)\u001b[22m\n      \u001b[2mat Object.mock (\u001b[22m\u001b[0m\u001b[36msrc/tests/unit/components/AuthContainer.test.jsx\u001b[39m\u001b[0m\u001b[2m:49:6)\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\AuthContainer.test.jsx", "startTime": 1751627601913, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1751627601913, "message": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[31m\u001b[1mConfiguration error\u001b[22m:\u001b[39m\n    \u001b[31m\u001b[39m\n    \u001b[31mCould not locate module \u001b[1m@/tests/unit/SignUpForm\u001b[22m mapped as:\u001b[39m\n    \u001b[31m\u001b[1mC:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\$1\u001b[22m.\u001b[39m\n    \u001b[31m\u001b[39m\n    \u001b[31mPlease check your configuration for these entries:\u001b[39m\n    \u001b[31m{\u001b[39m\n    \u001b[31m  \"moduleNameMapper\": {\u001b[39m\n    \u001b[31m    \"/^@\\/(.*)$/\": \"\u001b[1mC:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\$1\u001b[22m\"\u001b[39m\n    \u001b[31m  },\u001b[39m\n    \u001b[31m  \"resolver\": \u001b[1mundefined\u001b[22m\u001b[39m\n    \u001b[31m}\u001b[39m\n\n    \u001b[0m \u001b[90m 4 |\u001b[39m \u001b[36mimport\u001b[39m { toast } \u001b[36mfrom\u001b[39m \u001b[32m'sonner'\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m 5 |\u001b[39m \u001b[36mimport\u001b[39m axios \u001b[36mfrom\u001b[39m \u001b[32m'axios'\u001b[39m\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m \u001b[36mimport\u001b[39m \u001b[33mSignUpForm\u001b[39m \u001b[36mfrom\u001b[39m \u001b[32m'@/tests/unit/SignUpForm'\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m   |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 7 |\u001b[39m\n     \u001b[90m 8 |\u001b[39m \u001b[90m// Mock dependencies\u001b[39m\n     \u001b[90m 9 |\u001b[39m jest\u001b[33m.\u001b[39mmock(\u001b[32m'sonner'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat createNoMappedModuleFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:759:17)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36msrc/tests/unit/components/SignUpForm.real.test.tsx\u001b[39m\u001b[0m\u001b[2m:6:1)\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\SignUpForm.real.test.tsx", "startTime": 1751627601913, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1751627601913, "message": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[1m\u001b[31m<PERSON><PERSON> encountered an unexpected token\u001b[39m\u001b[22m\n\n    <PERSON><PERSON> failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when <PERSON><PERSON> is not configured to support such syntax.\n\n    Out of the box Je<PERSON> supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see \u001b[4mhttps://jestjs.io/docs/ecmascript-modules\u001b[24m for how to enable it.\n     • If you are trying to use TypeScript, see \u001b[4mhttps://jestjs.io/docs/getting-started#using-typescript\u001b[24m\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \u001b[1m\"transformIgnorePatterns\"\u001b[22m in your config.\n     • If you need a custom transformation specify a \u001b[1m\"transform\"\u001b[22m option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \u001b[1m\"moduleNameMapper\"\u001b[22m config option.\n\n    You'll find more details and examples of these config options in the docs:\n    \u001b[36mhttps://jestjs.io/docs/configuration\u001b[39m\n    For information about custom transformations, see:\n    \u001b[36mhttps://jestjs.io/docs/code-transformation\u001b[39m\n\n    \u001b[1m\u001b[31mDetails:\u001b[39m\u001b[22m\n\n    C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\config\\env.ts:16\n        return import.meta.env.VITE_APP_API_URL || 'http://localhost:8080';\n                      ^^^^\n\n    SyntaxError: Cannot use 'import.meta' outside a module\n\n    \u001b[0m \u001b[90m 13 |\u001b[39m \u001b[36mimport\u001b[39m \u001b[33mPropTypes\u001b[39m \u001b[36mfrom\u001b[39m \u001b[32m'prop-types'\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m 14 |\u001b[39m \u001b[36mimport\u001b[39m axios \u001b[36mfrom\u001b[39m \u001b[32m'axios'\u001b[39m\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m \u001b[36mimport\u001b[39m { getApiBaseUrl } \u001b[36mfrom\u001b[39m \u001b[32m'../config/env'\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 16 |\u001b[39m \u001b[36mimport\u001b[39m {\n     \u001b[90m 17 |\u001b[39m   \u001b[33mEMAIL_MESSAGES\u001b[39m\u001b[33m,\u001b[39m\n     \u001b[90m 18 |\u001b[39m   \u001b[33mSUCCESS_MESSAGES\u001b[39m\u001b[33m,\u001b[39m\u001b[0m\n\n      \u001b[2mat Runtime.createScriptFromCode (\u001b[22mnode_modules/jest-runtime/build/index.js\u001b[2m:1505:14)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22msrc/components/ForgotPasswordForm.jsx\u001b[2m:15:1)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36msrc/tests/unit/components/ForgotPasswordForm.real.test.jsx\u001b[39m\u001b[0m\u001b[2m:5:1)\u001b[22m\n", "name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ai-react-frontend\\src\\tests\\unit\\components\\ForgotPasswordForm.real.test.jsx", "startTime": 1751627601913, "status": "failed", "summary": ""}], "wasInterrupted": false}