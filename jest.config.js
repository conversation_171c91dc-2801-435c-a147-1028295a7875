export default {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  
  // Transform files
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },

  // File extensions to consider
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx'],

  // Test file patterns - prioritize tests/ folders and .test files
  testMatch: [
    '<rootDir>/src/tests/unit/**/*.(test).(js|jsx|ts|tsx)',
    '<rootDir>/src/tests/integration/**/*.(test).(js|jsx|ts|tsx)',
    '<rootDir>/src/tests/api/**/*.(test).(js|jsx|ts|tsx)'
  ],
  roots: ['<rootDir>/src', '<rootDir>/src/tests'],

  // Exclude mock test files and unnecessary folders when running coverage
  testPathIgnorePatterns: [
    '/node_modules/',
    '/tests/unit/components/ui/',
    '/src/components/ui/',
    '\\.(simple)\\.(js|jsx|ts|tsx)$'
  ],

  // Coverage configuration - focus on tested components (exclude ui and test files)
  collectCoverageFrom: [
    'src/**/*.(js|jsx|ts|tsx)',
    '!src/**/*.d.ts',
    '!src/main.jsx',
    '!src/vite-env.d.ts',
    '!src/components/ui/**',
    '!src/**/__tests__/**',
    '!src/**/*.test.*',
    '!src/**/*.spec.*',
  ],

  // Coverage thresholds - adjusted for realistic targets
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50
    }
  },

  // Module name mapping for path aliases and static assets
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/src/__mocks__/fileMock.js'
  },
  
  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true
};
