apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-cors-config
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: backend
    config-type: cors
data:
  # CORS configuration for Spring Boot application
  cors.allowed-origins: |
    http://localhost:3000,
    http://localhost:3001,
    http://*************:3000,
    http://ai-react-frontend-service.ai-react-frontend-dev.svc.cluster.local:3000,
    https://*************:3000
  cors.allowed-methods: "GET,POST,PUT,DELETE,OPTIONS,PATCH"
  cors.allowed-headers: |
    Accept,
    Accept-Language,
    Content-Language,
    Content-Type,
    Authorization,
    X-Requested-With,
    Origin,
    Access-Control-Request-Method,
    Access-Control-Request-Headers
  cors.allow-credentials: "true"
  cors.max-age: "3600"
  
  # Application properties for CORS
  application-cors.properties: |
    # CORS Configuration
    cors.allowed-origins=http://localhost:3000,http://localhost:3001,http://*************:3000,http://ai-react-frontend-service.ai-react-frontend-dev.svc.cluster.local:3000,https://*************:3000
    cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
    cors.allowed-headers=Accept,Accept-Language,Content-Language,Content-Type,Authorization,X-Requested-With,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
    cors.allow-credentials=true
    cors.max-age=3600
    
    # Security settings
    security.cors.enabled=true
    security.cors.path-pattern=/**
    
    # Logging
    logging.level.org.springframework.web.cors=DEBUG
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-env-config
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: backend
    config-type: environment
data:
  # Environment-specific configuration
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SERVER_PORT: "8080"
  
  # Frontend service URLs for internal communication
  FRONTEND_SERVICE_URL: "http://ai-react-frontend-service.ai-react-frontend-dev.svc.cluster.local:3000"
  FRONTEND_EXTERNAL_URL: "http://*************:3000"
  
  # Database and other service configurations can be added here
  # DATABASE_URL: "********************************************"
  
  # OAuth redirect URIs
  OAUTH_REDIRECT_URI_INTERNAL: "http://ai-react-frontend-service.ai-react-frontend-dev.svc.cluster.local:3000/oauth2/redirect"
  OAUTH_REDIRECT_URI_EXTERNAL: "http://*************:3000/oauth2/redirect"
