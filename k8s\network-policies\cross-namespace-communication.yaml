# NetworkPolicy to allow ai-react-frontend-dev to communicate with ai-spring-backend-dev
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-frontend-to-backend
  namespace: ai-spring-backend-dev
spec:
  podSelector:
    matchLabels:
      app: ai-spring-backend
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ai-react-frontend-dev
    - podSelector:
        matchLabels:
          app: ai-react-frontend
    ports:
    - protocol: TCP
      port: 8080
---
# NetworkPolicy to allow ai-spring-backend-dev to respond to ai-react-frontend-dev
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-backend-to-frontend
  namespace: ai-react-frontend-dev
spec:
  podSelector:
    matchLabels:
      app: ai-react-frontend
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ai-spring-backend-dev
    - podSelector:
        matchLabels:
          app: ai-spring-backend
    ports:
    - protocol: TCP
      port: 3000
---
# NetworkPolicy to allow external traffic to frontend
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-external-to-frontend
  namespace: ai-react-frontend-dev
spec:
  podSelector:
    matchLabels:
      app: ai-react-frontend
  policyTypes:
  - Ingress
  ingress:
  - {} # Allow all external traffic
    ports:
    - protocol: TCP
      port: 3000
---
# NetworkPolicy to allow external traffic to backend
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-external-to-backend
  namespace: ai-spring-backend-dev
spec:
  podSelector:
    matchLabels:
      app: ai-spring-backend
  policyTypes:
  - Ingress
  ingress:
  - {} # Allow all external traffic
    ports:
    - protocol: TCP
      port: 8080
