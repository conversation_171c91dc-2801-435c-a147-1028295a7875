apiVersion: v1
kind: Service
metadata:
  name: ai-react-frontend-service
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: frontend
    environment: dev
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app: ai-react-frontend
---
apiVersion: v1
kind: Service
metadata:
  name: ai-react-frontend-external
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: frontend
    environment: dev
    service-type: external
spec:
  type: LoadBalancer
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app: ai-react-frontend
  # This will create an external IP for accessing the frontend from outside the cluster
