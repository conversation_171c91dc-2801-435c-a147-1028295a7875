apiVersion: v1
kind: Service
metadata:
  name: ai-spring-backend-service
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: backend
    environment: dev
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: ai-spring-backend
---
apiVersion: v1
kind: Service
metadata:
  name: ai-spring-backend-external
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: backend
    environment: dev
    service-type: external
spec:
  type: LoadBalancer
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: ai-spring-backend
  # This will create an external IP for accessing the backend from outside the cluster
