{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"generate:envs": "node scripts/generate-env-files.js", "dev": "echo '❌ Please use one of the explicit backend scripts: dev:spring, dev:nest, dev:django, etc.' && exit 1", "build": "echo '❌ Please use one of the explicit backend scripts: build:spring, build:nest, build:django, etc.' && exit 1", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "dev:spring": "vite --mode spring", "dev:spring.beta": "vite --mode beta.spring", "dev:spring.production": "vite --mode production.spring", "build:spring": "vite build --mode spring", "build:spring.beta": "vite build --mode beta.spring", "build:spring.production": "vite build --mode production.spring", "dev:nest": "vite --mode nest", "dev:nest.beta": "vite --mode beta.nest", "dev:nest.production": "vite --mode production.nest", "build:nest": "vite build --mode nest", "build:nest.beta": "vite build --mode beta.nest", "build:nest.production": "vite build --mode production.nest", "dev:django": "vite --mode django", "dev:django.beta": "vite --mode beta.django", "dev:django.production": "vite --mode production.django", "build:django": "vite build --mode django", "build:django.beta": "vite build --mode beta.django", "build:django.production": "vite build --mode production.django", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --coverage", "sonar:start": "docker compose -f docker-compose.sonarqube.yml up -d", "sonar:stop": "docker compose -f docker-compose.sonarqube.yml stop", "sonar:clean": "docker compose -f docker-compose.sonarqube.yml down", "sonar:scan": "node ./node_modules/sonarqube-scanner/bin/sonar-scanner", "sonar:scan:cmd": "sonar-scan.cmd", "sonar:setup": "run-sonar.cmd"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.0", "@mui/material": "^7.1.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@babel/preset-env": "^7.25.9", "@babel/preset-react": "^7.25.9", "@babel/preset-typescript": "^7.25.9", "@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "babel-plugin-transform-import-meta": "^2.3.3", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "sonarqube-scanner": "^4.3.0", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}