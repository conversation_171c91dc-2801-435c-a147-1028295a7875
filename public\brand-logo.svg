<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">

  <rect width="80" height="80" rx="20" fill="#1976d2"/>

  <!-- React Atom -->

  <g>

    <ellipse cx="40" cy="40" rx="18" ry="7" stroke="#fff" stroke-width="2" fill="none"/>

    <ellipse cx="40" cy="40" rx="7" ry="18" stroke="#fff" stroke-width="2" fill="none"/>

    <ellipse cx="40" cy="40" rx="15" ry="5" stroke="#fff" stroke-width="2" fill="none" transform="rotate(45 40 40)"/>

    <circle cx="40" cy="40" r="4.5" fill="#fff" stroke="#1976d2" stroke-width="2"/>

  </g>

  <!-- AI/Robot Head -->

  <g>

    <rect x="28" y="48" width="24" height="14" rx="7" fill="#fff" stroke="#1976d2" stroke-width="2"/>

    <rect x="34" y="54" width="4" height="4" rx="2" fill="#1976d2"/>

    <rect x="42" y="54" width="4" height="4" rx="2" fill="#1976d2"/>

    <rect x="38" y="58" width="4" height="2" rx="1" fill="#1976d2"/>

  </g>

</svg>