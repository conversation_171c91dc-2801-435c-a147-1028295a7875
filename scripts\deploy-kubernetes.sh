#!/bin/bash

# Kubernetes Deployment Script for AI React Frontend Communication Fix
# This script deploys all necessary Kubernetes resources to enable communication
# between React frontend and Spring Boot backend

set -e

echo "🚀 Starting Kubernetes deployment for AI React Frontend communication fix..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

print_status "Connected to Kubernetes cluster"

# Step 1: Apply namespace labels
echo "📋 Step 1: Applying namespace labels..."
if kubectl apply -f k8s/namespaces/namespace-labels.yaml; then
    print_status "Namespace labels applied"
else
    print_warning "Namespace labels may already exist or failed to apply"
fi

# Step 2: Deploy services
echo "🌐 Step 2: Deploying services..."
if kubectl apply -f k8s/services/ai-react-frontend-service.yaml; then
    print_status "Frontend service deployed"
else
    print_error "Failed to deploy frontend service"
fi

if kubectl apply -f k8s/services/ai-spring-backend-service.yaml; then
    print_status "Backend service deployed"
else
    print_error "Failed to deploy backend service"
fi

# Step 3: Apply backend configuration
echo "⚙️  Step 3: Applying backend configuration..."
if kubectl apply -f k8s/configmaps/ai-spring-backend-cors-config.yaml; then
    print_status "Backend CORS configuration applied"
else
    print_error "Failed to apply backend configuration"
fi

# Step 4: Configure network policies
echo "🔒 Step 4: Configuring network policies..."
if kubectl apply -f k8s/network-policies/cross-namespace-communication.yaml; then
    print_status "Network policies applied"
else
    print_warning "Network policies may not be supported or failed to apply"
fi

# Step 5: Verify deployments
echo "🔍 Step 5: Verifying deployments..."

echo "Checking services in ai-react-frontend-dev namespace:"
kubectl get services -n ai-react-frontend-dev

echo "Checking services in ai-spring-backend-dev namespace:"
kubectl get services -n ai-spring-backend-dev

echo "Checking ConfigMaps in ai-spring-backend-dev namespace:"
kubectl get configmaps -n ai-spring-backend-dev

echo "Checking NetworkPolicies:"
kubectl get networkpolicies -n ai-react-frontend-dev
kubectl get networkpolicies -n ai-spring-backend-dev

print_status "Deployment completed!"

echo ""
echo "📖 Next Steps:"
echo "1. Update your Spring Boot backend deployment to use the ConfigMaps"
echo "2. Redeploy your applications using the new Kubernetes environment"
echo "3. Test the communication using the verification steps in docs/KUBERNETES_COMMUNICATION_SETUP.md"
echo ""
echo "🔗 For detailed testing instructions, see: docs/KUBERNETES_COMMUNICATION_SETUP.md"
