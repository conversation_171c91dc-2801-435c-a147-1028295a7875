// scripts/generate-env-files.js
import fs from 'fs';
import path from 'path';

// Environment configurations
const environments = {
  // Spring Boot environments
  'spring': { port: 8080 },
  'beta.spring': { port: 8080 },
  'production.spring': { port: 8080 },

  // Nest.js environments
  'nest': { port: 8080 },
  'beta.nest': { port: 8080 },
  'production.nest': { port: 8080 },

  // Django environments
  'django': { port: 8000 },
  'beta.django': { port: 8000 },
  'production.django': { port: 8000 },
};

// Generate environment files
Object.entries(environments).forEach(([mode, config]) => {
  const backendType = mode.split('.')[0];
  const viteEnv =
    mode.includes('production') ? 'production' :
    mode.includes('beta') ? 'beta' : 'local';

  const frontendPort = 3000;
  const oauthUrl = `http://localhost:${config.port}/oauth2/authorize/google?redirect_uri=http://localhost:${frontendPort}/oauth2/redirect`;

  const envContent = `VITE_APP_ENV=${viteEnv}
VITE_APP_API_URL=http://localhost:${config.port}
VITE_APP_PORT=${frontendPort}
VITE_APP_BACKEND_TYPE=${backendType}
VITE_APP_GOOGLE_OAUTH_URL=${oauthUrl}
`;

  const fileName = path.join(process.cwd(), `.env.${mode}`);
  fs.writeFileSync(fileName, envContent);
  console.log(`✅ Created ${fileName}`);
});
