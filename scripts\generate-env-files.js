// scripts/generate-env-files.js
import fs from 'fs';
import path from 'path';

// Environment configurations
const environments = {
  // Spring Boot environments
  'spring': { port: 8080 },
  'beta.spring': { port: 8080 },
  'production.spring': { port: 8080 },

  // Nest.js environments
  'nest': { port: 8080 },
  'beta.nest': { port: 8080 },
  'production.nest': { port: 8080 },

  // Django environments
  'django': { port: 8000 },
  'beta.django': { port: 8000 },
  'production.django': { port: 8000 },

  // Kubernetes environments (internal service communication)
  'kubernetes.spring': {
    port: 8080,
    useInternalService: true,
    serviceName: 'ai-spring-backend-service',
    namespace: 'ai-spring-backend-dev'
  },
  'kubernetes.nest': {
    port: 8080,
    useInternalService: true,
    serviceName: 'ai-nest-backend-service',
    namespace: 'ai-nest-backend-dev'
  },
  'kubernetes.django': {
    port: 8000,
    useInternalService: true,
    serviceName: 'ai-django-backend-service',
    namespace: 'ai-django-backend-dev'
  },
};

// Generate environment files
Object.entries(environments).forEach(([mode, config]) => {
  const backendType = mode.split('.')[0];
  const isKubernetes = mode.includes('kubernetes');
  const viteEnv =
    mode.includes('production') ? 'production' :
    mode.includes('beta') ? 'beta' :
    isKubernetes ? 'kubernetes' : 'local';

  const frontendPort = 3000;

  // Determine API URL based on environment
  let apiUrl;
  let oauthUrl;

  if (isKubernetes && config.useInternalService) {
    // Use Kubernetes internal service communication
    apiUrl = `http://${config.serviceName}.${config.namespace}.svc.cluster.local:${config.port}`;
    oauthUrl = `${apiUrl}/oauth2/authorize/google?redirect_uri=http://ai-react-frontend-service.ai-react-frontend-dev.svc.cluster.local:${frontendPort}/oauth2/redirect`;
  } else {
    // Use localhost for local development
    apiUrl = `http://localhost:${config.port}`;
    oauthUrl = `${apiUrl}/oauth2/authorize/google?redirect_uri=http://localhost:${frontendPort}/oauth2/redirect`;
  }

  const envContent = `VITE_APP_ENV=${viteEnv}
VITE_APP_API_URL=${apiUrl}
VITE_APP_PORT=${frontendPort}
VITE_APP_BACKEND_TYPE=${backendType}
VITE_APP_GOOGLE_OAUTH_URL=${oauthUrl}
${isKubernetes ? `VITE_APP_KUBERNETES_MODE=true\nVITE_APP_SERVICE_NAME=${config.serviceName}\nVITE_APP_BACKEND_NAMESPACE=${config.namespace}` : ''}
`;

  const fileName = path.join(process.cwd(), `.env.${mode}`);
  fs.writeFileSync(fileName, envContent);
  console.log(`✅ Created ${fileName}`);
});
