{"version": "1.128.1", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": ".github\\workflows\\semgrep-scan.yml", "start": {"line": 37, "col": 86, "offset": 1114}, "end": {"line": 37, "col": 89, "offset": 1117}}]], "message": "Syntax error at line .github\\workflows\\semgrep-scan.yml:37:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": ".github\\workflows\\semgrep-scan.yml", "spans": [{"file": ".github\\workflows\\semgrep-scan.yml", "start": {"line": 37, "col": 86, "offset": 1114}, "end": {"line": 37, "col": 89, "offset": 1117}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": ".github\\workflows\\docker-push.yml", "start": {"line": 26, "col": 48, "offset": 760}, "end": {"line": 26, "col": 51, "offset": 763}}]], "message": "Syntax error at line .github\\workflows\\docker-push.yml:26:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": ".github\\workflows\\docker-push.yml", "spans": [{"file": ".github\\workflows\\docker-push.yml", "start": {"line": 26, "col": 48, "offset": 760}, "end": {"line": 26, "col": 51, "offset": 763}}]}], "paths": {"scanned": [".dockerignore", ".env.example", ".github\\workflows\\docker-push.yml", ".github\\workflows\\npm-build-and-test.yml", ".github\\workflows\\pipeline.yml", ".github\\workflows\\semgrep-scan.yml", ".github\\workflows\\sonarqube-analysis.yml", ".giti<PERSON>re", ".semgrepignore", "babel.config.cjs", "components.json", "docker-compose.sonarqube.yml", "docs\\CODE_CLEANUP_SUMMARY.md", "docs\\COMPLETE_CHANGES_LOG.md", "docs\\CONTRIBUTING.md", "docs\\DOCKER-COMMANDS.md", "docs\\DOCKERHUB-COMMANDS.md", "docs\\DOCKERIZE-README.md", "docs\\ENV_SETUP.md", "docs\\FORGOT_PASSWORD_NAVIGATION_FIX.md", "docs\\FORGOT_PASSWORD_UPDATE.md", "docs\\IMPLEMENTATION_SUMMARY.md", "docs\\RESET_PASSWORD_IMPLEMENTATION.md", "docs\\ROUTING_UPDATE_SUMMARY.md", "docs\\SECURITY.md", "docs\\SELECT_TABS_TESTING_COMPLETE.md", "docs\\SONARQUBE_EXECUTION_STEPS.md", "docs\\SONARQUBE_SETUP.md", "docs\\SONARQUBE_VERIFICATION.md", "docs\\TEST_SUMMARY.md", "docs\\TESTING_GUIDE.md", "docs\\TODAYS_COMPLETE_CHANGES.md", "docs\\URL_NAVIGATION_FIX.md", "eslint.config.js", "index.html", "jest-results.json", "jest.config.js", "k8s\\arc\\runner-deployment.yaml", "k8s\\semgrep-job.yaml", "k8s\\semgrep-results-reader.yaml", "nginx.conf", "package-lock.json", "package.json", "postcss.config.js", "README.md", "scripts\\generate-env-files.js", "semgrep-results.json", "sonar-project.properties", "sonar-scanner.js", "src\\__mocks__\\fileMock.js", "src\\App.jsx", "src\\components\\AuthContainer.jsx", "src\\components\\common\\AlertMessage.jsx", "src\\components\\common\\AuthFormFooter.jsx", "src\\components\\common\\AuthFormHeader.jsx", "src\\components\\common\\BasePasswordInput.jsx", "src\\components\\common\\ConfirmPasswordInput.jsx", "src\\components\\common\\EmailInput.jsx", "src\\components\\common\\FormContainer.jsx", "src\\components\\common\\FormHeader.jsx", "src\\components\\common\\index.js", "src\\components\\common\\PasswordFormSection.jsx", "src\\components\\common\\PasswordInput.jsx", "src\\components\\common\\SubmitButton.jsx", "src\\components\\common\\TermsCheckbox.jsx", "src\\components\\common\\TextInput.jsx", "src\\components\\ConfirmationPage.jsx", "src\\components\\ForgotPasswordForm.jsx", "src\\components\\GoogleLoginButton.jsx", "src\\components\\LoginForm.jsx", "src\\components\\ProtectedRoute.jsx", "src\\components\\RootRedirect.jsx", "src\\components\\SignUpForm.tsx", "src\\components\\ui\\accessibility-demo.tsx", "src\\components\\ui\\accordion.tsx", "src\\components\\ui\\alert.tsx", "src\\components\\ui\\badge.tsx", "src\\components\\ui\\button.tsx", "src\\components\\ui\\card.tsx", "src\\components\\ui\\checkbox.tsx", "src\\components\\ui\\dialog.tsx", "src\\components\\ui\\form.tsx", "src\\components\\ui\\input.tsx", "src\\components\\ui\\label.tsx", "src\\components\\ui\\select.tsx", "src\\components\\ui\\sonner.tsx", "src\\components\\ui\\tabs.tsx", "src\\components\\ui\\toast.tsx", "src\\components\\ui\\toaster.tsx", "src\\components\\ui\\tooltip.tsx", "src\\components\\ui\\use-toast.ts", "src\\components\\UserDashboard.jsx", "src\\config\\env.ts", "src\\config\\testConfig.js", "src\\constants\\validationMessages.js", "src\\contexts\\AuthContext.tsx", "src\\hooks\\index.js", "src\\hooks\\use-mobile.tsx", "src\\hooks\\use-toast.ts", "src\\hooks\\useApiCall.js", "src\\hooks\\useDeviceInfo.js", "src\\hooks\\useFormValidation.js", "src\\hooks\\usePasswordValidation.js", "src\\index.css", "src\\lib\\utils.ts", "src\\main.jsx", "src\\pages\\CreatePassword.jsx", "src\\pages\\ForgotPassword.jsx", "src\\pages\\NotFound.tsx", "src\\pages\\OAuth2Redirect.jsx", "src\\pages\\Profile.jsx", "src\\pages\\ResetPassword.jsx", "src\\pages\\SignIn.jsx", "src\\pages\\SignUp.jsx", "src\\setupTests.js", "src\\tests\\integration\\ProtectedRoute.test.jsx", "src\\tests\\integration\\RootRedirect.test.jsx", "src\\tests\\unit\\components\\AuthContainer.real.test.jsx", "src\\tests\\unit\\components\\common\\AlertMessage.test.jsx", "src\\tests\\unit\\components\\common\\AuthFormFooter.test.jsx", "src\\tests\\unit\\components\\common\\AuthFormHeader.test.jsx", "src\\tests\\unit\\components\\common\\BasePasswordInput.test.jsx", "src\\tests\\unit\\components\\common\\EmailInput.test.jsx", "src\\tests\\unit\\components\\common\\FormContainer.test.jsx", "src\\tests\\unit\\components\\common\\FormHeader.test.jsx", "src\\tests\\unit\\components\\common\\PasswordFormSection.test.jsx", "src\\tests\\unit\\components\\common\\PasswordInput.test.jsx", "src\\tests\\unit\\components\\common\\SubmitButton.test.jsx", "src\\tests\\unit\\components\\common\\TermsCheckbox.test.jsx", "src\\tests\\unit\\components\\common\\TextInput.test.jsx", "src\\tests\\unit\\components\\LoginForm.test.jsx", "src\\tests\\unit\\components\\UserDashboard.test.jsx", "src\\tests\\unit\\contexts\\AuthContext.real.test.tsx", "src\\tests\\unit\\contexts\\AuthContext.simple.test.tsx", "src\\tests\\unit\\hooks\\use-toast.test.ts", "src\\tests\\unit\\hooks\\useDeviceInfo.test.js", "src\\tests\\unit\\hooks\\usePasswordValidation.test.js", "src\\tests\\unit\\lib\\utils.test.ts", "src\\tests\\unit\\pages\\CreatePassword.test.jsx", "src\\tests\\unit\\pages\\ForgotPassword.test.jsx", "src\\tests\\unit\\pages\\NotFound.test.tsx", "src\\tests\\unit\\pages\\OAuth2Redirect.test.jsx", "src\\tests\\unit\\pages\\ResetPassword.test.jsx", "src\\tests\\unit\\pages\\SignIn.test.jsx", "src\\tests\\unit\\pages\\SignUp.test.jsx", "src\\tests\\unit\\setup.test.js", "src\\vite-env.d.ts", "tailwind.config.ts", "tsconfig.app.json", "tsconfig.json", "tsconfig.node.json", "vite.config.ts"]}, "time": {"rules": [], "rules_parse_time": 1.9321422576904297, "profiling_times": {"config_time": 1.1775968074798584, "core_time": 8.08168363571167, "ignores_time": 0.0017156600952148438, "total_time": 9.262060642242432}, "parsing_time": {"total_time": 1.0076255798339844, "per_file_time": {"mean": 0.008192077884829145, "std_dev": 0.00032976888459709835}, "very_slow_files": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1129746496}, "skipped_rules": []}