#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import http from 'http';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to check if SonarQube is running
function checkSonarQubeStatus() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:9000/api/system/status', (res) => {
      if (res.statusCode === 200) {
        resolve(true);
      } else {
        reject(new Error(`HTTP ${res.statusCode}`));
      }
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Timeout'));
    });
  });
}

async function main() {
  console.log('========================================');
  console.log('SonarQube Analysis (Node.js)');
  console.log('========================================');
  console.log('');

  // Check if SonarQube is running
  console.log('[1/4] Checking if SonarQube is running...');
  try {
    await checkSonarQubeStatus();
    console.log('✅ SonarQube is running');
  } catch (error) {
    console.log('❌ SonarQube is not running on localhost:9000');
    console.log('Please start SonarQube first: npm run sonar:start');
    console.log('Or run the setup: npm run sonar:setup');
    process.exit(1);
  }

  // Check if coverage report exists
  console.log('[2/4] Checking test coverage...');
  const coveragePath = path.join(__dirname, 'coverage', 'lcov.info');
  if (!fs.existsSync(coveragePath)) {
    console.log('⚠️  Coverage report not found. Running tests with coverage...');
    try {
      execSync('npm run test:coverage', { stdio: 'inherit' });
      console.log('✅ Tests completed with coverage');
    } catch (error) {
      console.log('❌ Tests failed. Please fix tests before running SonarQube analysis.');
      process.exit(1);
    }
  } else {
    console.log('✅ Coverage report found');
  }

  console.log('[3/4] Starting SonarQube analysis...');

  // Try different methods to run sonar-scanner
  const scannerMethods = [
    () => {
      console.log('Trying npx sonar-scanner...');
      execSync('npx sonar-scanner', { stdio: 'inherit' });
    },
    () => {
      console.log('Trying sonar-scanner from PATH...');
      execSync('sonar-scanner', { stdio: 'inherit' });
    },
    () => {
      console.log('Trying sonar-scanner.cmd from PATH...');
      execSync('sonar-scanner.cmd', { stdio: 'inherit' });
    },
    () => {
      console.log('Trying local sonarqube-scanner...');
      const scannerPath = path.join(__dirname, 'node_modules', 'sonarqube-scanner', 'bin', 'sonar-scanner');
      execSync(`node "${scannerPath}"`, { stdio: 'inherit' });
    }
  ];

  let success = false;
  for (const method of scannerMethods) {
    try {
      method();
      success = true;
      break;
    } catch (error) {
      // Continue to next method
      continue;
    }
  }

  if (!success) {
    console.log('❌ ERROR: SonarQube Scanner not found!');
    console.log('');
    console.log('[4/4] Installation Instructions:');
    console.log('');
    console.log('Option 1 - Download SonarQube Scanner:');
    console.log('1. Go to: https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/');
    console.log('2. Download the CLI scanner for Windows');
    console.log('3. Extract to C:\\sonar-scanner (or any location)');
    console.log('4. Add C:\\sonar-scanner\\bin to your PATH environment variable');
    console.log('5. Restart command prompt and run: npm run sonar:scan');
    console.log('');
    console.log('Option 2 - Install via npm:');
    console.log('npm install -g sonarqube-scanner');
    console.log('');
    console.log('Option 3 - Use Docker (if available):');
    console.log('docker run --rm -v "%cd%":/usr/src sonarsource/sonar-scanner-cli');
    console.log('');
    console.log('Current project configuration:');
    console.log('- Project Key: ai-react-frontend');
    console.log('- SonarQube URL: http://localhost:9000');
    console.log('- Source: src/');
    console.log('- Coverage: coverage/lcov.info');
    console.log('');
    process.exit(1);
  }

  console.log('');
  console.log('========================================');
  console.log('✅ SonarQube Analysis Complete!');
  console.log('========================================');
  console.log('');
  console.log('📊 View results at: http://localhost:9000');
  console.log('🔍 Project: ai-react-frontend');
  console.log('');
  console.log('Next steps:');
  console.log('- Review code quality metrics');
  console.log('- Fix any issues found');
  console.log('- Re-run analysis after fixes');
  console.log('');
}

// Run the main function
main().catch((error) => {
  console.error('Error:', error);
  process.exit(1);
});
