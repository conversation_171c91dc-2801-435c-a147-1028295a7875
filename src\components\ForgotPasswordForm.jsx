import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  InputAdornment,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Mail } from 'lucide-react';
import PropTypes from 'prop-types';
import axios from 'axios';
import { getApiBaseUrl } from '../config/env';
import {
  EMAIL_MESSAGES,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  BUTTON_TEXT,
  FORM_LABELS,
  REGEX_PATTERNS
} from '../constants/validationMessages';

const ForgotPasswordForm = ({ onSwitchToLogin }) => {
  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState({});
  const [emailSent, setEmailSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get API URL from environment
  const API_BASE_URL = getApiBaseUrl();

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    if (!email) {
      newErrors.email = EMAIL_MESSAGES.REQUIRED;
    } else if (!REGEX_PATTERNS.EMAIL.test(email)) {
      newErrors.email = EMAIL_MESSAGES.INVALID;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (value) => {
    setEmail(value);
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: '' }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // Get IP address
      const fetchIpAddress = async () => {
        try {
          const res = await axios.get("https://api.ipify.org?format=json");
          return res.data.ip;
        } catch {
          return "";
        }
      };

      const ipAddress = await fetchIpAddress();
      const deviceDetails = navigator.userAgent;

      // Call forgot password API with camelCase payload
      const payload = {
        email,
        ipAddress,
        deviceDetails
      };

      await axios.post(`${API_BASE_URL}/api/v1/forgot-password`, payload, {
        headers: { "Content-Type": "application/json" },
        validateStatus: () => true, // Accept all status codes
      });

      // Always show the same message for security reasons (both success and failure)
      setEmailSent(true);

    } catch (error) {
      // Even on error, show the same security message
      setEmailSent(true);
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <Box sx={{ width: '100%' }}>
        <Typography variant="h5" component="h1" gutterBottom sx={{
          fontWeight: 700,
          color: '#1a1a1a',
          mb: 1,
          textAlign: 'center',
          fontSize: { xs: '1.3rem', sm: '1.5rem' }
        }}>
          Check Your Email
        </Typography>

        <Typography variant="body2" sx={{
          color: '#666',
          mb: 3,
          textAlign: 'center',
          fontSize: '0.875rem'
        }}>
          Password reset request submitted
        </Typography>

        <Alert severity="info" sx={{ mb: 2.5, borderRadius: 1.5, fontSize: '0.875rem' }}>
          <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
            <strong>If the email exists in our system, we'll send you a link to reset your password.</strong><br />
            Please check your inbox and spam folder for the reset link.
          </Typography>
        </Alert>

        <Box sx={{ mb: 2.5 }}>
          <Typography variant="body2" sx={{ color: '#666', mb: 1.5, fontSize: '0.85rem' }}>
            Didn't receive the email?
          </Typography>
          <Button
            variant="outlined"
            onClick={() => setEmailSent(false)}
            size="small"
            sx={{
              borderRadius: 1.5,
              textTransform: 'none',
              fontWeight: 600,
              px: 2,
              py: 0.75,
              fontSize: '0.875rem',
            }}
          >
            {BUTTON_TEXT.TRY_AGAIN}
          </Button>
        </Box>

        <Typography variant="body2" sx={{ textAlign: 'center', color: '#666', fontSize: '0.85rem' }}>
          Remember your password?{' '}
          <Link
            component="button"
            type="button"
            onClick={onSwitchToLogin}
            sx={{ 
              color: '#1976d2',
              textDecoration: 'none',
              fontWeight: 600,
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            Back to sign in
          </Link>
        </Typography>
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <Typography variant="h5" component="h1" gutterBottom sx={{ 
        fontWeight: 700, 
        color: '#1a1a1a',
        mb: 1,
        textAlign: 'center',
        fontSize: { xs: '1.3rem', sm: '1.5rem' }
      }}>
        Forgot Password
      </Typography>
      
      <Typography variant="body2" sx={{ 
        color: '#666', 
        mb: 3, 
        textAlign: 'center',
        fontSize: '0.875rem'
      }}>
        Enter your email address and we'll send you a link to reset your password
      </Typography>

      <TextField
        fullWidth
        label={FORM_LABELS.EMAIL}
        type="email"
        value={email}
        onChange={(e) => handleInputChange(e.target.value)}
        error={!!errors.email}
        helperText={errors.email}
        disabled={isLoading}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Mail size={18} color="#666" />
            </InputAdornment>
          ),
        }}
        sx={{ 
          mb: 2.5,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1.5,
          },
        }}
      />

      <Button
        fullWidth
        type="submit"
        variant="contained"
        disabled={isLoading}
        size="medium"
        sx={{
          py: 1.25,
          fontSize: '0.9rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: 1.5,
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          boxShadow: '0 3px 10px rgba(25, 118, 210, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
            boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)',
          },
          '&:disabled': {
            background: '#ccc',
            boxShadow: 'none',
          },
        }}
      >
        {isLoading ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          BUTTON_TEXT.SEND_RESET_LINK
        )}
      </Button>

      <Typography variant="body2" sx={{ mt: 2.5, textAlign: 'center', color: '#666', fontSize: '0.85rem' }}>
        Remember your password?{' '}
        <Link
          component="button"
          type="button"
          onClick={onSwitchToLogin}
          sx={{ 
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 600,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          Back to sign in
        </Link>
      </Typography>
    </Box>
  );
};

ForgotPasswordForm.propTypes = {
  onSwitchToLogin: PropTypes.func.isRequired,
};

export default ForgotPasswordForm;
