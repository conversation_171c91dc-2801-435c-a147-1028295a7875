import React from 'react';
import { useState } from 'react';
import {
  Box,
  Link,
} from '@mui/material';
import PropTypes from 'prop-types';
import { useAuth } from '../contexts/AuthContext';
import {
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  BUTTON_TEXT,
  FORM_LABELS,
} from '../constants/validationMessages';
import {
  EmailInput,
  PasswordInput,
  SubmitButton,
  AlertMessage,
  AuthFormHeader,
  AuthFormFooter,
} from './common';
import { useFormValidation, useApiCall } from '../hooks';

const LoginForm = ({ onSwitchToSignUp, onSwitchToForgotPassword }) => {
  const { login, isLoading } = useAuth();

  // Form validation hook
  const formValidation = useFormValidation({
    email: '',
    password: '',
  });

  // API call hook
  const apiCall = useApiCall();
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formValidation.validateForm(['email', 'password'])) {
      return;
    }

    const loginCall = async () => {
      await login(formValidation.values.email, formValidation.values.password);
      return { success: true };
    };

    await apiCall.execute(loginCall, {
      successMessage: SUCCESS_MESSAGES.LOGIN_SUCCESS,
      errorMessage: ERROR_MESSAGES.INVALID_CREDENTIALS,
    });
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <AuthFormHeader
        title="Welcome Back"
        subtitle="Sign in to your account to continue"
        showLogo={false}
      />

      <EmailInput
        label={FORM_LABELS.EMAIL}
        value={formValidation.values.email}
        onChange={(e) => formValidation.handleInputChange('email', e.target.value)}
        onBlur={() => formValidation.handleBlur('email')}
        error={!!formValidation.errors.email}
        helperText={formValidation.errors.email}
        disabled={isLoading}
        sx={{ mb: 1.5 }}
      />

      <PasswordInput
        label={FORM_LABELS.AUTH_FIELD}
        value={formValidation.values.password}
        onChange={(e) => formValidation.handleInputChange('password', e.target.value)}
        onBlur={() => formValidation.handleBlur('password')}
        error={!!formValidation.errors.password}
        helperText={formValidation.errors.password}
        disabled={isLoading}
        sx={{ mb: 1.5 }}
      />

      <Box sx={{ textAlign: 'right', mb: 2.5 }}>
        <Link
          component="button"
          type="button"
          variant="body2"
          onClick={onSwitchToForgotPassword}
          sx={{
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 500,
            fontSize: '0.8rem',
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          Forgot password?
        </Link>
      </Box>

      <AlertMessage
        message={apiCall.error}
        severity="error"
        show={!!apiCall.error}
      />

      <SubmitButton
        disabled={isLoading}
        loading={apiCall.loading}
        sx={{ authStyle: true }}
      >
        {BUTTON_TEXT.SIGN_IN}
      </SubmitButton>

      <AuthFormFooter
        message="Don't have an account?"
        linkText="Sign up"
        onLinkClick={onSwitchToSignUp}
      />
    </Box>
  );
};

LoginForm.propTypes = {
  onSwitchToSignUp: PropTypes.func.isRequired,
  onSwitchToForgotPassword: PropTypes.func.isRequired,
};

export default LoginForm;
