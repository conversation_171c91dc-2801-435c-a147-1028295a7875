import React from 'react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { CircularProgress, Box } from '@mui/material';

const RootRedirect = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Small delay to ensure auth context has loaded
    const timer = setTimeout(() => {
      if (user) {
        // User is authenticated, redirect to dashboard
        navigate('/dashboard', { replace: true });
      } else {
        // User is not authenticated, redirect to signin
        navigate('/signin', { replace: true });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [user, navigate]);

  // Show loading spinner while determining redirect
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      }}
    >
      <CircularProgress size={40} sx={{ color: 'white' }} />
    </Box>
  );
};

export default RootRedirect;
