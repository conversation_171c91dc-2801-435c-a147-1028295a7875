import React from 'react';
import { useState } from 'react';
import {
  Box,
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import ConfirmationPage from './ConfirmationPage';
import axios from 'axios';
import { getApiBaseUrl } from '../config/env';
import {
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  BUTTON_TEXT,
  FORM_LABELS,
  VALIDATION_MESSAGES
} from '../constants/validationMessages';
import {
  EmailInput,
  TextInput,
  SubmitButton,
  AlertMessage,
  AuthFormHeader,
  AuthFormFooter,
  TermsCheckbox,
} from './common';
import { useFormValidation, useApiCall, useDeviceInfo } from '../hooks';

interface SignUpFormProps {
  onSwitchToLogin: () => void;
}

const SignUpForm: React.FC<SignUpFormProps> = ({ onSwitchToLogin }) => {
  const [showConfirmation, setShowConfirmation] = useState(false);

  const { signUp, isLoading } = useAuth();

  // Form validation hook
  const formValidation = useFormValidation({
    name: '',
    email: '',
    mobile: '',
    agreeToTerms: false,
  });

  // API call hook
  const apiCall = useApiCall();

  // Device info hook
  const { getDeviceInfo } = useDeviceInfo();

  // Get API URL from environment
  const API_BASE_URL = getApiBaseUrl();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValidation.validateForm(['name', 'email', 'mobile', 'terms'])) {
      return;
    }

    const signUpCall = async () => {
      const payload = {
        name: formValidation.values.name.trim(),
        email: formValidation.values.email,
        mobileNumber: formValidation.values.mobile,
      };

      const response = await axios.post(`${API_BASE_URL}/api/v1/register`, payload, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true,
      });

      if (response.status === 200 || response.status === 201) {
        setShowConfirmation(true);
        return response.data;
      } else if (response.status === 409) {
        throw new Error(response.data.error || ERROR_MESSAGES.EMAIL_ALREADY_REGISTERED);
      } else {
        throw new Error(ERROR_MESSAGES.ACCOUNT_CREATION_FAILED);
      }
    };

    await apiCall.execute(signUpCall, {
      successMessage: SUCCESS_MESSAGES.ACCOUNT_CREATED,
      errorMessage: ERROR_MESSAGES.ACCOUNT_CREATION_FAILED,
    });
  };

  if (showConfirmation) {
    return <ConfirmationPage />;
  }

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <AuthFormHeader
        title="Create Account"
        subtitle="Join us today and get started"
        showLogo={false}
      />

      <TextInput
        label={FORM_LABELS.FULL_NAME}
        value={formValidation.values.name}
        onChange={(e) => formValidation.handleInputChange('name', e.target.value)}
        onBlur={() => formValidation.handleBlur('name')}
        error={!!formValidation.errors.name}
        helperText={formValidation.errors.name}
        disabled={isLoading}
        icon="user"
        sx={{ mb: 1.5 }}
      />

      <EmailInput
        label={FORM_LABELS.EMAIL}
        value={formValidation.values.email}
        onChange={(e) => formValidation.handleInputChange('email', e.target.value)}
        onBlur={() => formValidation.handleBlur('email')}
        error={!!formValidation.errors.email}
        helperText={formValidation.errors.email}
        disabled={isLoading}
        sx={{ mb: 1.5 }}
      />

      <TextInput
        label={FORM_LABELS.MOBILE}
        value={formValidation.values.mobile}
        onChange={(e) => formValidation.handleInputChange('mobile', e.target.value)}
        onBlur={() => formValidation.handleBlur('mobile')}
        error={!!formValidation.errors.mobile}
        helperText={formValidation.errors.mobile}
        disabled={isLoading}
        icon="mobile"
        sx={{ mb: 1.5 }}
      />

      <TermsCheckbox
        checked={formValidation.values.agreeToTerms}
        onChange={(e) => formValidation.handleInputChange('agreeToTerms', e.target.checked)}
        disabled={isLoading}
        error={!!formValidation.errors.agreeToTerms}
      />

      <AlertMessage
        message={formValidation.errors.agreeToTerms}
        severity="error"
        show={!!formValidation.errors.agreeToTerms}
        sx={{ mb: 1.5, fontSize: '0.75rem' }}
      />

      <AlertMessage
        message={apiCall.error}
        severity="error"
        show={!!apiCall.error}
      />

      <SubmitButton
        disabled={isLoading}
        loading={apiCall.loading}
        sx={{ authStyle: true }}
      >
        {BUTTON_TEXT.CREATE_ACCOUNT}
      </SubmitButton>

      <AuthFormFooter
        message="Already have an account?"
        linkText="Sign in"
        onLinkClick={onSwitchToLogin}
      />
    </Box>
  );
};

export default SignUpForm;
