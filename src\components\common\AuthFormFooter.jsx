import React from 'react';
import { Typography, <PERSON> } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * Reusable footer component for authentication forms
 * Displays a message with a link to switch between login/signup
 */
const AuthFormFooter = ({ 
  message, 
  linkText, 
  onLinkClick 
}) => {
  return (
    <Typography variant="body2" sx={{ mt: 2.5, textAlign: 'center', color: '#666', fontSize: '0.85rem' }}>
      {message}{' '}
      <Link
        component="button"
        type="button"
        onClick={onLinkClick}
        sx={{ 
          color: '#1976d2',
          textDecoration: 'none',
          fontWeight: 600,
          '&:hover': {
            textDecoration: 'underline',
          },
        }}
      >
        {linkText}
      </Link>
    </Typography>
  );
};

AuthFormFooter.propTypes = {
  message: PropTypes.string.isRequired,
  linkText: PropTypes.string.isRequired,
  onLinkClick: PropTypes.func.isRequired,
};

export default AuthFormFooter;
