import React from 'react';
import { Box, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import GoogleLoginButton from '../GoogleLoginButton';

/**
 * Reusable header component for authentication forms
 * Includes logo, title, subtitle, and optional Google login with divider
 */
const AuthFormHeader = ({ 
  title, 
  subtitle, 
  showLogo = true, 
  showGoogleLogin = true,
  logoSrc = "/brand-logo.svg",
  logoAlt = "Brand Logo"
}) => {
  return (
    <>
      {showLogo && (
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <img
            src={logoSrc}
            alt={logoAlt}
            style={{ width: 80, height: 80, display: 'block', margin: '0 auto 16px auto' }}
          />
        </Box>
      )}
      
      <Typography variant="h5" component="h1" gutterBottom sx={{ 
        fontWeight: 700, 
        color: '#1a1a1a',
        mb: 1,
        textAlign: 'center',
        fontSize: { xs: '1.3rem', sm: '1.5rem' }
      }}>
        {title}
      </Typography>
      
      <Typography variant="body2" sx={{ 
        color: '#666', 
        mb: 3, 
        textAlign: 'center',
        fontSize: '0.875rem'
      }}>
        {subtitle}
      </Typography>

      {showGoogleLogin && (
        <>
          <GoogleLoginButton />
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            my: 2.5,
            '&::before, &::after': {
              content: '""',
              flex: 1,
              height: '1px',
              backgroundColor: '#e0e0e0',
            },
          }}>
            <Typography sx={{ px: 2, color: '#666', fontSize: '0.8rem' }}>
              or continue with email
            </Typography>
          </Box>
        </>
      )}
    </>
  );
};

AuthFormHeader.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired,
  showLogo: PropTypes.bool,
  showGoogleLogin: PropTypes.bool,
  logoSrc: PropTypes.string,
  logoAlt: PropTypes.string,
};

export default AuthFormHeader;
