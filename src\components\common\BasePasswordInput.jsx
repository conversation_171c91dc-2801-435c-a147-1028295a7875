import React, { useState } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { Eye, EyeOff, Lock } from 'lucide-react';
import PropTypes from 'prop-types';

/**
 * Base password input component with show/hide functionality
 * Used as foundation for PasswordInput and ConfirmPasswordInput
 */
const BasePasswordInput = ({
  label,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  disabled = false,
  autoComplete = "current-password",
  showStartAdornment = true,
  placeholder,
  size = "small",
  sx = {},
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <TextField
      fullWidth
      label={label}
      type={showPassword ? 'text' : 'password'}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      error={error}
      helperText={helperText}
      disabled={disabled}
      autoComplete={autoComplete}
      placeholder={placeholder}
      size={size}
      InputProps={{
        startAdornment: showStartAdornment && (
          <InputAdornment position="start">
            <Lock size={18} color="#666" />
          </InputAdornment>
        ),
        endAdornment: value && value.length > 0 && (
          <InputAdornment position="end">
            <IconButton
              onClick={handleTogglePassword}
              edge="end"
              size="small"
              disabled={disabled}
            >
              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </IconButton>
          </InputAdornment>
        ),
      }}
      sx={{
        '& .MuiOutlinedInput-root': {
          borderRadius: 1.5,
        },
        ...sx,
      }}
      {...props}
    />
  );
};

BasePasswordInput.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func,
  error: PropTypes.bool,
  helperText: PropTypes.string,
  disabled: PropTypes.bool,
  autoComplete: PropTypes.string,
  showStartAdornment: PropTypes.bool,
  placeholder: PropTypes.string,
  size: PropTypes.string,
  sx: PropTypes.object,
};

export default BasePasswordInput;
