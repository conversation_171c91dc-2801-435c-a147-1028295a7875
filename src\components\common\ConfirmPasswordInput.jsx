import React from 'react';
import PropTypes from 'prop-types';
import BasePasswordInput from './BasePasswordInput';

/**
 * Confirm password input component for registration and password reset forms
 * Uses new-password autoComplete and has default label
 */
const ConfirmPasswordInput = ({
  label = "Confirm Password",
  autoComplete = "new-password",
  ...props
}) => {
  return (
    <BasePasswordInput
      label={label}
      autoComplete={autoComplete}
      {...props}
    />
  );
};

ConfirmPasswordInput.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func,
  error: PropTypes.bool,
  helperText: PropTypes.string,
  disabled: PropTypes.bool,
  autoComplete: PropTypes.string,
  showStartAdornment: PropTypes.bool,
  placeholder: PropTypes.string,
  size: PropTypes.string,
  sx: PropTypes.object,
};

export default ConfirmPasswordInput;
