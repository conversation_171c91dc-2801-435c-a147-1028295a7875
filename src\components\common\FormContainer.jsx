import React from 'react';
import { Box, Paper } from '@mui/material';
import PropTypes from 'prop-types';

const FormContainer = ({ 
  children, 
  maxWidth = 480,
  showLogo = false,
  sx = {},
  ...props 
}) => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: { xs: 2, sm: 3 },
        px: { xs: 1, sm: 2 },
        ...sx,
      }}
      {...props}
    >
      <Paper
        elevation={24}
        sx={{
          p: { xs: 3, sm: 4 },
          borderRadius: 2,
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          width: '100%',
          maxWidth,
          mx: 'auto',
        }}
      >
        {showLogo && (
          <Box sx={{ textAlign: 'center', mb: 2 }}>
            <img
              src="/brand-logo.svg"
              alt="Brand Logo"
              style={{ 
                width: 80, 
                height: 80, 
                display: 'block', 
                margin: '0 auto 16px auto' 
              }}
            />
          </Box>
        )}
        {children}
      </Paper>
    </Box>
  );
};

FormContainer.propTypes = {
  children: PropTypes.node.isRequired,
  maxWidth: PropTypes.number,
  showLogo: PropTypes.bool,
  sx: PropTypes.object,
};

export default FormContainer;
