import React from 'react';
import { Typography } from '@mui/material';
import PropTypes from 'prop-types';

const FormHeader = ({
  title,
  subtitle,
  titleSx = {},
  subtitleSx = {},
  ...props
}) => {
  return (
    <>
      {title && (
        <Typography
          variant="h5"
          component="h1"
          gutterBottom
          sx={{
            fontWeight: 700,
            color: '#1a1a1a',
            mb: 1,
            textAlign: 'center',
            fontSize: { xs: '1.3rem', sm: '1.5rem' },
            ...titleSx,
          }}
          {...props}
        >
          {title}
        </Typography>
      )}
      
      {subtitle && (
        <Typography
          variant="body2"
          sx={{
            color: '#666',
            mb: 3,
            textAlign: 'center',
            fontSize: '0.95rem',
            ...subtitleSx,
          }}
        >
          {subtitle}
        </Typography>
      )}
    </>
  );
};

FormHeader.propTypes = {
  title: PropTypes.string,
  subtitle: PropTypes.string,
  titleSx: PropTypes.object,
  subtitleSx: PropTypes.object,
};

export default FormHeader;
