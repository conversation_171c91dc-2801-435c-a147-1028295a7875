import React from 'react';
import { Box } from '@mui/material';
import PropTypes from 'prop-types';
import PasswordInput from './PasswordInput';
import ConfirmPasswordInput from './ConfirmPasswordInput';
import SubmitButton from './SubmitButton';
import AlertMessage from './AlertMessage';

/**
 * Reusable password form section component
 * Used for password creation and reset forms
 */
const PasswordFormSection = ({
  // Form props
  onSubmit,
  
  // Password state
  newPassword,
  confirmPassword,
  onPasswordChange,
  onConfirmPasswordChange,
  onPasswordBlur,
  onConfirmPasswordBlur,
  
  // Validation
  passwordValidation,
  
  // Labels
  passwordLabel,
  confirmPasswordLabel,
  
  // Button
  buttonText,
  buttonDisabled,
  buttonLoading,
  
  // Error handling
  apiError,
  
  // Disabled state
  disabled = false,
  
  // Styling
  sx = {}
}) => {
  return (
    <Box component="form" onSubmit={onSubmit} sx={sx}>
      <PasswordInput
        label={passwordLabel}
        value={newPassword}
        onChange={onPasswordChange}
        onBlur={onPasswordBlur}
        autoComplete="new-password"
        error={passwordValidation.hasPasswordError}
        helperText={
          passwordValidation.hasPasswordError
            ? passwordValidation.passwordErrors[0]
            : ''
        }
        disabled={disabled}
        sx={{ mb: 2 }}
      />

      <ConfirmPasswordInput
        label={confirmPasswordLabel}
        value={confirmPassword}
        onChange={onConfirmPasswordChange}
        onBlur={onConfirmPasswordBlur}
        autoComplete="new-password"
        error={passwordValidation.hasConfirmPasswordError}
        helperText={
          passwordValidation.hasConfirmPasswordError
            ? passwordValidation.confirmPasswordError
            : ''
        }
        disabled={disabled}
        sx={{ mb: 2 }}
      />

      <AlertMessage
        message={apiError}
        severity="error"
        show={!!apiError}
      />

      <SubmitButton
        disabled={buttonDisabled}
        loading={buttonLoading}
      >
        {buttonText}
      </SubmitButton>
    </Box>
  );
};

PasswordFormSection.propTypes = {
  // Form props
  onSubmit: PropTypes.func.isRequired,
  
  // Password state
  newPassword: PropTypes.string.isRequired,
  confirmPassword: PropTypes.string.isRequired,
  onPasswordChange: PropTypes.func.isRequired,
  onConfirmPasswordChange: PropTypes.func.isRequired,
  onPasswordBlur: PropTypes.func.isRequired,
  onConfirmPasswordBlur: PropTypes.func.isRequired,
  
  // Validation
  passwordValidation: PropTypes.shape({
    hasPasswordError: PropTypes.bool.isRequired,
    passwordErrors: PropTypes.array.isRequired,
    hasConfirmPasswordError: PropTypes.bool.isRequired,
    confirmPasswordError: PropTypes.string,
  }).isRequired,
  
  // Labels
  passwordLabel: PropTypes.string.isRequired,
  confirmPasswordLabel: PropTypes.string.isRequired,
  
  // Button
  buttonText: PropTypes.string.isRequired,
  buttonDisabled: PropTypes.bool.isRequired,
  buttonLoading: PropTypes.bool.isRequired,
  
  // Error handling
  apiError: PropTypes.string,
  
  // Disabled state
  disabled: PropTypes.bool,
  
  // Styling
  sx: PropTypes.object,
};

export default PasswordFormSection;
