import React from 'react';
import { Button, CircularProgress } from '@mui/material';
import PropTypes from 'prop-types';

const SubmitButton = ({
  children,
  loading = false,
  disabled = false,
  variant = "contained",
  size = "medium",
  fullWidth = true,
  type = "submit",
  sx = {},
  ...props
}) => {
  const defaultSx = {
    py: 1.25,
    fontSize: '0.95rem',
    fontWeight: 600,
    textTransform: 'none',
    borderRadius: 1.5,
    background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.15)',
    mb: 1.5,
    '&:hover': {
      background: 'linear-gradient(90deg, #5a67d8 0%, #6b47b6 100%)',
    },
    '&:disabled': {
      background: '#ccc',
      boxShadow: 'none',
    },
    ...sx,
  };

  // For login/signup forms, use different gradient
  const isAuthButton = sx.authStyle;
  if (isAuthButton) {
    defaultSx.background = 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)';
    defaultSx.boxShadow = '0 3px 10px rgba(25, 118, 210, 0.3)';
    defaultSx['&:hover'] = {
      background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
      boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)',
    };
    delete defaultSx.authStyle;
  }

  return (
    <Button
      fullWidth={fullWidth}
      type={type}
      variant={variant}
      size={size}
      disabled={disabled || loading}
      sx={defaultSx}
      {...props}
    >
      {loading ? (
        <CircularProgress size={20} color="inherit" />
      ) : (
        children
      )}
    </Button>
  );
};

SubmitButton.propTypes = {
  children: PropTypes.node.isRequired,
  loading: PropTypes.bool,
  disabled: PropTypes.bool,
  variant: PropTypes.string,
  size: PropTypes.string,
  fullWidth: PropTypes.bool,
  type: PropTypes.string,
  sx: PropTypes.object,
};

export default SubmitButton;
