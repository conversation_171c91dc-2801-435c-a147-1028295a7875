import React from 'react';
import {
  TextField,
  InputAdornment,
} from '@mui/material';
import PropTypes from 'prop-types';

const TextInput = ({
  label,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  disabled = false,
  autoComplete,
  type = "text",
  startAdornment,
  endAdornment,
  placeholder,
  size = "small",
  sx = {},
  ...props
}) => {
  return (
    <TextField
      fullWidth
      label={label}
      type={type}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      error={error}
      helperText={helperText}
      disabled={disabled}
      autoComplete={autoComplete}
      placeholder={placeholder}
      size={size}
      InputProps={{
        startAdornment: startAdornment && (
          <InputAdornment position="start">
            {startAdornment}
          </InputAdornment>
        ),
        endAdornment: endAdornment && (
          <InputAdornment position="end">
            {endAdornment}
          </InputAdornment>
        ),
      }}
      sx={{
        '& .MuiOutlinedInput-root': {
          borderRadius: 1.5,
        },
        ...sx,
      }}
      {...props}
    />
  );
};

TextInput.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func,
  error: PropTypes.bool,
  helperText: PropTypes.string,
  disabled: PropTypes.bool,
  autoComplete: PropTypes.string,
  type: PropTypes.string,
  startAdornment: PropTypes.node,
  endAdornment: PropTypes.node,
  placeholder: PropTypes.string,
  size: PropTypes.string,
  sx: PropTypes.object,
};

export default TextInput;
