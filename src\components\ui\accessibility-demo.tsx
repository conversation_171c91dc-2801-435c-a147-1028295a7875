import React from 'react';
import { Alert, AlertTitle, AlertDescription } from './alert';
import { Card, CardTitle, CardDescription, CardContent } from './card';

/**
 * Demo component showing the accessibility improvements for heading components.
 * 
 * BEFORE: Empty headings would render as <h5></h5> or <h3></h3> which violates
 * accessibility guidelines as screen readers cannot understand empty headings.
 * 
 * AFTER: Empty headings are not rendered at all, ensuring only meaningful
 * headings are present in the DOM for screen readers.
 */
export const AccessibilityDemo: React.FC = () => {
  return (
    <div className="space-y-6 p-6">
      <h1>Accessibility Improvements Demo</h1>
      
      <section>
        <h2>Alert Components</h2>
        
        {/* This will render properly with content */}
        <Alert className="mb-4">
          <AlertTitle>Success!</AlertTitle>
          <AlertDescription>
            This alert has both title and description content.
          </AlertDescription>
        </Alert>
        
        {/* This will NOT render empty heading elements */}
        <Alert className="mb-4">
          <AlertTitle>{null}</AlertTitle> {/* Will not render */}
          <AlertTitle>{undefined}</AlertTitle> {/* Will not render */}
          <AlertDescription>
            This alert has description but no title heading will be rendered.
          </AlertDescription>
        </Alert>
        
        {/* This will render with valid content */}
        <Alert className="mb-4">
          <AlertTitle>Warning</AlertTitle>
          <AlertDescription>
            This alert has a proper title.
          </AlertDescription>
        </Alert>
      </section>
      
      <section>
        <h2>Card Components</h2>
        
        {/* This will render properly with content */}
        <Card className="mb-4">
          <CardContent>
            <CardTitle>Card with Title</CardTitle>
            <CardDescription>
              This card has both title and description content.
            </CardDescription>
          </CardContent>
        </Card>
        
        {/* This will NOT render empty heading elements */}
        <Card className="mb-4">
          <CardContent>
            <CardTitle>{null}</CardTitle> {/* Will not render */}
            <CardTitle>{undefined}</CardTitle> {/* Will not render */}
            <CardDescription>
              This card has description but no title heading will be rendered.
            </CardDescription>
          </CardContent>
        </Card>
        
        {/* Edge cases that should still render */}
        <Card className="mb-4">
          <CardContent>
            <CardTitle>{""}</CardTitle> {/* Empty string - will render */}
            <CardTitle>{0}</CardTitle> {/* Zero - will render */}
            <CardDescription>
              Empty string and zero are considered valid content.
            </CardDescription>
          </CardContent>
        </Card>
      </section>
      
      <section>
        <h2>Accessibility Benefits</h2>
        <ul className="list-disc pl-6 space-y-2">
          <li>
            <strong>Screen Reader Friendly:</strong> No empty headings that confuse screen readers
          </li>
          <li>
            <strong>Semantic HTML:</strong> Only meaningful headings are rendered in the DOM
          </li>
          <li>
            <strong>WCAG Compliance:</strong> Meets accessibility guidelines for heading content
          </li>
          <li>
            <strong>Developer Experience:</strong> Components gracefully handle null/undefined children
          </li>
          <li>
            <strong>Backward Compatible:</strong> Existing code with valid content continues to work
          </li>
        </ul>
      </section>
    </div>
  );
};

export default AccessibilityDemo;
