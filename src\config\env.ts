// Environment configuration helper
export const getApiBaseUrl = (): string => {
  // For testing environment
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
    return process.env.VITE_APP_API_URL || 'http://localhost:8080';
  }
  
  // For runtime environment
  try {
    return import.meta.env.VITE_APP_API_URL || 'http://localhost:8080';
  } catch {
    return 'http://localhost:8080';
  }
};
