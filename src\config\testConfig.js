// Secure Test Configuration
// This file manages test data securely using environment variables

/**
 * Get test password from environment variables
 * Falls back to secure defaults only for development
 * @param {string} type - Type of password needed
 * @returns {string} - Test password
 */
const getTestPassword = (type) => {
  // In production/CI, these should come from secure environment variables
  const testPasswords = {
    VALID: process.env.TEST_VALID_PASSWORD || 'TestPass123!',
    STRONG: process.env.TEST_STRONG_PASSWORD || 'StrongTest123!@#',
    WEAK: process.env.TEST_WEAK_PASSWORD || 'weak',
    SHORT: process.env.TEST_SHORT_PASSWORD || '123',
    DIFFERENT: process.env.TEST_DIFFERENT_PASSWORD || 'DifferentTest123!',
  };

  return testPasswords[type] || 'DefaultTest123!';
};

/**
 * Secure test data configuration
 * Passwords are loaded from environment variables
 */
export const SECURE_TEST_DATA = {
  // Non-sensitive test data
  VALID_EMAIL: "<EMAIL>",
  VALID_NAME: "Test User",
  VALID_MOBILE: "1234567890",
  INVALID_EMAIL: "invalid-email",
  INVALID_MOBILE: "123",
  
  // Sensitive data loaded from environment
  PASSWORDS: {
    get VALID() { return getTestPassword('VALID'); },
    get STRONG() { return getTestPassword('STRONG'); },
    get WEAK() { return getTestPassword('WEAK'); },
    get SHORT() { return getTestPassword('SHORT'); },
    get DIFFERENT() { return getTestPassword('DIFFERENT'); },
  },
  
  // Legacy support (deprecated - use PASSWORDS object)
  get VALID_PASSWORD() { return getTestPassword('VALID'); },
  get VALID_STRONG_PASSWORD() { return getTestPassword('STRONG'); },
  get WEAK_PASSWORD() { return getTestPassword('WEAK'); },
  get SHORT_PASSWORD() { return getTestPassword('SHORT'); },
};

// Warning for developers
if (process.env.NODE_ENV === 'development') {

}
