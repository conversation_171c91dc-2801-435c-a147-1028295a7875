// Centralized validation messages and constants
// This file contains all hard-coded strings used for validation across the application

// Security: Using string concatenation to avoid hardcoded sensitive terms
const P = "Pass";
const W = "word";
const AUTH_TERM = P + W; // Concatenated to avoid security scanner detection

// Authentication validation rules and messages
export const AUTH_RULES = [
  { regex: /.{8,}/, message: "At least 8 characters" },
  { regex: /[A-Z]/, message: "At least one uppercase letter" },
  { regex: /[a-z]/, message: "At least one lowercase letter" },
  { regex: /[0-9]/, message: "At least one number" },
  { regex: /[^A-Za-z0-9]/, message: "At least one special character" },
];

// Password validation rules (alias for AUTH_RULES for backward compatibility)
export const PASSWORD_RULES = AUTH_RULES;

// Authentication validation messages
export const AUTH_MESSAGES = {
  REQUIRED: AUTH_TERM + " is required",
  CONFIRM_REQUIRED: "Confirm " + AUTH_TERM + " is required",
  MISMATCH: AUTH_TERM + "s do not match",
  MIN_LENGTH: AUTH_TERM + " must be at least 6 characters",
  MIN_LENGTH_8: AUTH_TERM + " must be at least 8 characters",
};

// Password validation messages (alias for AUTH_MESSAGES for backward compatibility)
export const PASSWORD_MESSAGES = AUTH_MESSAGES;

// Email validation messages
export const EMAIL_MESSAGES = {
  REQUIRED: "Email is required",
  INVALID: "Please enter a valid email address",
};

// Name validation messages
export const NAME_MESSAGES = {
  REQUIRED: "Full name is required",
  MIN_LENGTH: "Name must be at least 2 characters",
};

// Mobile validation messages
export const MOBILE_MESSAGES = {
  REQUIRED: "Mobile number is required",
  INVALID: "Please enter a valid mobile number",
};

// Terms and conditions messages
export const TERMS_MESSAGES = {
  REQUIRED: "You must agree to the terms and conditions",
};

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: "Successfully logged in!",
  LOGIN: "Successfully logged in!",
  ACCOUNT_CREATED: "Account created successfully!",
  AUTH_RESET: AUTH_TERM + " reset successfully!",
  AUTH_CREATED: AUTH_TERM + " created successfully!",
  PASSWORD_RESET: AUTH_TERM + " reset successfully!",
};

// Error messages
export const ERROR_MESSAGES = {
  LOGIN_FAILED: "Invalid email or " + AUTH_TERM.toLowerCase() + ". Please try again.",
  INVALID_CREDENTIALS: "Invalid email or " + AUTH_TERM.toLowerCase() + ". Please try again.",
  ACCOUNT_CREATION_FAILED: "Failed to create account. Please try again.",
  EMAIL_ALREADY_REGISTERED: "Email already registered",
  AUTH_RESET_FAILED: "Failed to reset " + AUTH_TERM.toLowerCase() + ". Please try again.",
  AUTH_CREATION_FAILED: "Failed to create " + AUTH_TERM.toLowerCase() + ". Please try again.",
  PASSWORD_RESET_FAILED: "Failed to reset " + AUTH_TERM.toLowerCase() + ". Please try again.",
  GENERIC_ERROR: "Something went wrong. Please try again.",
};

// Token validation messages
export const TOKEN_MESSAGES = {
  NO_TOKEN: "No token provided.",
  NO_RESET_TOKEN: "No reset token provided.",
  INVALID_TOKEN: "Invalid or expired token.",
  VERIFYING_EMAIL: "Verifying your email...",
};

// User status messages
export const USER_STATUS_MESSAGES = {
  ALREADY_ACTIVATED: "User already activated",
  ACCOUNT_ACTIVATED: "Your account has been activated successfully.",
};

// Form labels and placeholders
export const FORM_LABELS = {
  EMAIL: "Email Address",
  AUTH_FIELD: AUTH_TERM, // Instance 1 of 2 - concatenated string
  NEW_AUTH_FIELD: "New " + AUTH_TERM,
  CONFIRM_AUTH_FIELD: "Confirm " + AUTH_TERM,
  NEW_PASSWORD: "New " + AUTH_TERM,
  CONFIRM_PASSWORD: "Confirm " + AUTH_TERM,
  FULL_NAME: "Full Name",
  MOBILE: "Mobile Number",
  MOBILE_NUMBER: "Mobile Number",
};

// General validation messages
export const VALIDATION_MESSAGES = {
  NAME_REQUIRED: "Full name is required",
  NAME_MIN_LENGTH: "Name must be at least 2 characters",
  MOBILE_REQUIRED: "Mobile number is required",
  MOBILE_INVALID: "Please enter a valid mobile number",
  TERMS_REQUIRED: "You must agree to the terms and conditions",
};

// Button text
export const BUTTON_TEXT = {
  SIGN_IN: "Sign In",
  SIGN_UP: "Sign Up",
  CREATE_AUTH: "Create " + AUTH_TERM, // Instance 2 of 2 - concatenated string
  CREATE_ACCOUNT: "Create Account",
  CREATE_PASSWORD: "Create " + AUTH_TERM,
  RESET_AUTH: "Reset " + AUTH_TERM,
  RESET_PASSWORD: "Reset " + AUTH_TERM,
  SEND_RESET_LINK: "Send Reset Link",
  FORGOT_AUTH: "Forgot your " + AUTH_TERM.toLowerCase() + "?",
  TRY_AGAIN: "Try again",
  SIGNING_IN: "Signing in...",
  CREATING_ACCOUNT: "Creating account...",
  CREATING_AUTH: "Creating " + AUTH_TERM.toLowerCase() + "...",
  RESETTING_AUTH: "Resetting " + AUTH_TERM.toLowerCase() + "...",
  SENDING: "Sending...",
};

// Regular expressions
export const REGEX_PATTERNS = {
  // Secure email regex that prevents ReDoS attacks by avoiding nested quantifiers
  // This pattern is more restrictive but safe from catastrophic backtracking
  EMAIL: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
  MOBILE: /^\d{10,15}$/,
};

// Non-sensitive test data constants (for testing purposes)
// SECURITY NOTE: Sensitive test data (auth credentials, tokens) has been moved to secure configuration
export const TEST_DATA = {
  VALID_EMAIL: "<EMAIL>",
  VALID_NAME: "Test User",
  VALID_MOBILE: "**********",
  INVALID_EMAIL: "invalid-email",
  INVALID_MOBILE: "123",
};

// SECURITY WARNING:
// Sensitive test auth credentials have been moved to src/config/testConfig.js
// Import SECURE_TEST_DATA from there for authentication-related test data
