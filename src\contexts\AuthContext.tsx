import React, { createContext, useContext, useState, ReactNode } from 'react';
import axios from 'axios';
import { getApiBaseUrl } from '../config/env';

interface User {
  id: string;
  email: string;
  name: string;
  profilePicture?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signUp: (name: string, email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => void;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  setAuthenticatedUser: (user: User) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Get API URL from environment
  const API_BASE_URL = getApiBaseUrl();

  // Check for existing authentication on app load
  React.useEffect(() => {
    const checkExistingAuth = () => {
      const userId = localStorage.getItem('userId');
      const sessionToken = localStorage.getItem('sessionToken');

      if (userId && sessionToken) {
        // Create user object from stored data
        const storedUser: User = {
          id: userId,
          email: localStorage.getItem('userEmail') || '',
          name: localStorage.getItem('userName') || '',
          profilePicture: localStorage.getItem('userProfilePicture') || undefined,
        };
        setUser(storedUser);
      }
    };

    checkExistingAuth();
  }, []);

  const setAuthenticatedUser = (userData: User) => {
    setUser(userData);
    // Store user data in localStorage for persistence
    localStorage.setItem('userId', userData.id);
    localStorage.setItem('userEmail', userData.email);
    localStorage.setItem('userName', userData.name);
    if (userData.profilePicture) {
      localStorage.setItem('userProfilePicture', userData.profilePicture);
    }
  };

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Get IP address
      const fetchIpAddress = async () => {
        try {
          const res = await axios.get("https://api.ipify.org?format=json");
          return res.data.ip;
        } catch {
          return "";
        }
      };

      const ipAddress = await fetchIpAddress();
      const deviceDetails = navigator.userAgent;

      // Call login API with camelCase payload
      const payload = {
        email,
        password,
        ipAddress,
        deviceDetails,
        overrideExistingLogins: true
      };

      const response = await axios.post(`${API_BASE_URL}/api/v1/login`, payload, {
        // headers: { "Content-Type": "application/json" },
        validateStatus: () => true,
      });

      if (response.status === 200 || response.status === 201) {
        // Store tokens in localStorage using camelCase response fields
        const { userId, sessionToken, refreshToken, expiresAt, refreshExpiresAt } = response.data;
        localStorage.setItem("userId", userId);
        localStorage.setItem("sessionToken", sessionToken);
        localStorage.setItem("refreshToken", refreshToken);
        localStorage.setItem("expiresAt", expiresAt);
        localStorage.setItem("refreshExpiresAt", refreshExpiresAt);

        // Create user object and set in context
        const userData: User = {
          id: userId,
          email: email,
          name: email.split('@')[0], // Fallback to email prefix for name
          profilePicture: undefined
        };

        setUser(userData);

        // Store user data for persistence
        localStorage.setItem('userEmail', email);
        localStorage.setItem('userName', userData.name);
      } else {
        throw new Error(response.data?.error || 'Login failed');
      }
    } catch (error) {
      throw new Error('Invalid credentials or connection error');
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (name: string, email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      // Do NOT setUser here!
    } catch (error) {
      throw new Error('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async (): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate Google OAuth flow
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock successful Google login
      const mockUser: User = {
        id: 'google_1',
        email: '<EMAIL>',
        name: 'Google User',
        profilePicture: 'https://via.placeholder.com/40',
      };
      
      setUser(mockUser);
    } catch (error) {
      throw new Error('Google authentication failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = (): void => {
    setUser(null);
    // Clear all user data from localStorage
    localStorage.removeItem('userId');
    localStorage.removeItem('sessionToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('expiresAt');
    localStorage.removeItem('refreshExpiresAt');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userName');
    localStorage.removeItem('userProfilePicture');
  };

  const forgotPassword = async (email: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
    } catch (error) {
      throw new Error('Failed to send reset email');
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (token: string, newPassword: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
    } catch (error) {
      throw new Error('Failed to reset password');
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    signUp,
    loginWithGoogle,
    logout,
    forgotPassword,
    resetPassword,
    setAuthenticatedUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
