import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';

const useApiCall = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const loadingRef = useRef(false);

  const execute = useCallback(async (apiCall, options = {}) => {
    const {
      onSuccess,
      onError,
      successMessage,
      errorMessage,
      showToast = true,
      resetOnStart = true,
    } = options;

    if (resetOnStart) {
      setError('');
      setSuccess(false);
    }

    // Prevent multiple simultaneous calls
    if (loadingRef.current) return;
    loadingRef.current = true;
    setLoading(true);

    try {
      const result = await apiCall();
      setSuccess(true);
      if (showToast && successMessage) {
        toast.success(successMessage);
      }
      if (onSuccess) {
        onSuccess(result);
      }
      return result;
    } catch (err) {
      const finalErrorMessage = errorMessage || err.message || 'Something went wrong. Please try again.';
      setError(finalErrorMessage);
      if (showToast) {
        toast.error(finalErrorMessage);
      }
      if (onError) {
        onError(err);
      }
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, []);

  const reset = useCallback(() => {
    setLoading(false);
    setError('');
    setSuccess(false);
    loadingRef.current = false;
  }, []);

  return {
    loading,
    error,
    success,
    execute,
    reset,
    setError,
    setSuccess,
  };
};

export default useApiCall;
