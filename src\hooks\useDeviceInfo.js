import { useState, useCallback } from 'react';
import axios from 'axios';

const useDeviceInfo = () => {
  const [ipAddress, setIpAddress] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchIpAddress = useCallback(async () => {
    if (ipAddress) return ipAddress; // Return cached value if available

    setLoading(true);
    try {
      const response = await axios.get('https://api.ipify.org?format=json');
      const ip = response.data.ip;
      setIpAddress(ip);
      return ip;
    } catch (error) {

      return '';
    } finally {
      setLoading(false);
    }
  }, [ipAddress]);

  const getDeviceDetails = useCallback(() => {
    return navigator.userAgent;
  }, []);

  const getDeviceInfo = useCallback(async () => {
    const ip = await fetchIpAddress();
    const deviceDetails = getDeviceDetails();
    
    return {
      ipAddress: ip,
      deviceDetails,
    };
  }, [fetchIpAddress, getDeviceDetails]);

  return {
    ipAddress,
    loading,
    fetchIpAddress,
    getDeviceDetails,
    getDeviceInfo,
  };
};

export default useDeviceInfo;
