import { useState, useCallback, useMemo } from 'react';
import { EMAIL_MESSAGES, REGEX_PATTERNS, VALIDATION_MESSAGES } from '../constants/validationMessages';

const useFormValidation = (initialValues = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  const validateField = useCallback((field, value) => {
    let error = '';

    switch (field) {
      case 'email':
        if (!value) {
          error = EMAIL_MESSAGES.REQUIRED;
        } else if (!REGEX_PATTERNS.EMAIL.test(value)) {
          error = EMAIL_MESSAGES.INVALID;
        }
        break;
      case 'name':
        if (!value?.trim()) {
          error = VALIDATION_MESSAGES.NAME_REQUIRED;
        } else if (value.trim().length < 2) {
          error = VALIDATION_MESSAGES.NAME_MIN_LENGTH;
        }
        break;
      case 'mobile':
        if (!value) {
          error = VALIDATION_MESSAGES.MOBILE_REQUIRED;
        } else if (!REGEX_PATTERNS.MOBILE.test(value)) {
          error = VALIDATION_MESSAGES.MOBILE_INVALID;
        }
        break;
      case 'agreeToTerms':
        if (!value) {
          error = VALIDATION_MESSAGES.TERMS_REQUIRED;
        }
        break;
      default:
        break;
    }

    return error;
  }, []);

  const validateForm = useCallback((fieldsToValidate = Object.keys(values)) => {
    const newErrors = {};
    let isValid = true;

    fieldsToValidate.forEach(field => {
      const error = validateField(field, values[field]);
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [values, validateField]);

  const handleInputChange = useCallback((field, value) => {
    setValues(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  const handleBlur = useCallback((field) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    
    // Validate this field on blur
    const error = validateField(field, values[field]);
    if (error) {
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  }, [values, validateField]);

  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  }, [initialValues]);

  const setFieldValue = useCallback((field, value) => {
    setValues(prev => ({ ...prev, [field]: value }));
  }, []);

  const setFieldError = useCallback((field, error) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  }, []);

  const setFieldTouched = useCallback((field, isTouched = true) => {
    setTouched(prev => ({ ...prev, [field]: isTouched }));
  }, []);

  const isValid = useMemo(() => Object.keys(errors).length === 0, [errors]);

  return {
    values,
    errors,
    touched,
    validateForm,
    handleInputChange,
    handleBlur,
    resetForm,
    setFieldValue,
    setFieldError,
    setFieldTouched,
    isValid,
  };
};

export default useFormValidation;
