import React from "react";
import { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import axios from "axios";
import { getApiBaseUrl } from "../config/env";
import {
  TOKEN_MESSAGES,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  BUTTON_TEXT,
  FORM_LABELS
} from "../constants/validationMessages";
import {
  FormContainer,
  FormHeader,
  AlertMessage,
  PasswordFormSection,
} from "../components/common";
import { usePasswordValidation, useApiCall, useDeviceInfo } from "../hooks";

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const resetToken = searchParams.get("token");

  // Get API URL from environment
  const API_BASE_URL = getApiBaseUrl();

  // State for token validation
  const [tokenLoading, setTokenLoading] = useState(true);
  const [tokenError, setTokenError] = useState("");
  // Add state for password reset error
  const [resetError, setResetError] = useState("");

  // Password state
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [touched, setTouched] = useState({ password: false, confirmPassword: false });

  // Custom hooks
  const passwordValidation = usePasswordValidation(newPassword, confirmPassword, touched);
  const apiCall = useApiCall();
  const { getDeviceInfo } = useDeviceInfo();

  // Token validation effect
  useEffect(() => {
    const validateToken = async () => {
      setTokenLoading(true);
      setTokenError("");

      if (!resetToken) {
        setTokenError(TOKEN_MESSAGES.NO_RESET_TOKEN);
        setTokenLoading(false);
        return;
      }

      // For now, we'll assume the token is valid if it exists
      // In a real implementation, you might want to validate the token with the backend
      setTokenLoading(false);
    };

    validateToken();
  }, [resetToken]);

  // Check if form can be submitted
  const canResetPassword = passwordValidation.isValid && !tokenLoading && !tokenError;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setTouched({ password: true, confirmPassword: true });

    if (!passwordValidation.isValid || !resetToken || tokenError) {
      return;
    }

    setResetError(""); // Clear previous error

    const resetPasswordCall = async () => {
      const deviceInfo = await getDeviceInfo();
      const payload = {
        resetToken,
        newPassword,
        ...deviceInfo,
      };

      const response = await axios.post(`${API_BASE_URL}/api/v1/reset-password`, payload, {
        headers: { "Content-Type": "application/json" },
        validateStatus: () => true,
      });

      if (response.status === 200 || response.status === 201) {
        // Redirect to sign in page after success
        setTimeout(() => {
          navigate("/signin");
        }, 2000);
        return response.data;
      } else {
        const errorMessage = response.data?.error || ERROR_MESSAGES.PASSWORD_RESET_FAILED;
        setResetError(errorMessage); // Set error state instead of throwing
        return null;
      }
    };

    try {
      await apiCall.execute(resetPasswordCall, {
        successMessage: SUCCESS_MESSAGES.PASSWORD_RESET,
        errorMessage: ERROR_MESSAGES.PASSWORD_RESET_FAILED,
      });
    } catch (err) {
      setResetError(err.message || ERROR_MESSAGES.PASSWORD_RESET_FAILED);
    }
  };

  return (
    <FormContainer>
      <FormHeader
        title={BUTTON_TEXT.RESET_PASSWORD}
        subtitle="Please enter your new password"
      />

      <AlertMessage
        message="Validating reset token..."
        severity="info"
        show={tokenLoading}
      />

      <AlertMessage
        message={tokenError}
        severity="error"
        show={!!tokenError}
      />

      <AlertMessage
        message={SUCCESS_MESSAGES.PASSWORD_RESET}
        severity="success"
        show={apiCall.success}
      />

      {/* Show reset error if present */}
      <AlertMessage
        message={resetError}
        severity="error"
        show={!!resetError}
      />

      {!apiCall.success && (
        <PasswordFormSection
          onSubmit={handleSubmit}
          newPassword={newPassword}
          confirmPassword={confirmPassword}
          onPasswordChange={e => setNewPassword(e.target.value)}
          onConfirmPasswordChange={e => setConfirmPassword(e.target.value)}
          onPasswordBlur={() => setTouched(t => ({ ...t, password: true }))}
          onConfirmPasswordBlur={() => setTouched(t => ({ ...t, confirmPassword: true }))}
          passwordValidation={passwordValidation}
          passwordLabel={FORM_LABELS.NEW_PASSWORD}
          confirmPasswordLabel={FORM_LABELS.CONFIRM_PASSWORD}
          buttonText={BUTTON_TEXT.RESET_PASSWORD}
          buttonDisabled={!canResetPassword}
          buttonLoading={apiCall.loading}
          apiError={apiCall.error}
          disabled={tokenLoading || !!tokenError}
        />
      )}
    </FormContainer>
  );
};

export default ResetPassword;
