import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import RootRedirect from '@/components/RootRedirect';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock AuthContext
const mockUseAuth = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, ...props }) => <div data-testid="loading-box" {...props}>{children}</div>,
  CircularProgress: ({ size, sx }) => (
    <div data-testid="loading-spinner" data-size={size} data-color={sx?.color} />
  ),
}));

describe('RootRedirect', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('Loading State', () => {
    test('should show loading spinner while determining redirect', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      expect(screen.getByTestId('loading-box')).toBeInTheDocument();
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    test('should display loading spinner with correct styling', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toHaveAttribute('data-size', '40');
      expect(spinner).toHaveAttribute('data-color', 'white');
    });

    test('should display loading box with correct background gradient', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      const loadingBox = screen.getByTestId('loading-box');
      expect(loadingBox).toBeInTheDocument();
    });
  });

  describe('Authentication-based Navigation', () => {
    test('should navigate to dashboard when user is authenticated', async () => {
      // Arrange
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    test('should navigate to signin when user is not authenticated', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    test('should navigate to dashboard when user object exists', async () => {
      // Arrange
      const mockUser = { 
        id: 'user123', 
        email: '<EMAIL>', 
        name: 'John Doe',
        profilePicture: 'https://example.com/pic.jpg'
      };
      mockUseAuth.mockReturnValue({ user: mockUser });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    test('should navigate to signin when user is undefined', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: undefined });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });
  });

  describe('Timer and Cleanup', () => {
    test('should use setTimeout with 100ms delay', async () => {
      // Arrange
      jest.useFakeTimers();
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert - navigation should not happen immediately
      expect(mockNavigate).not.toHaveBeenCalled();

      // Fast-forward time
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });

      jest.useRealTimers();
    });

    test('should cleanup timer on unmount', () => {
      // Arrange
      jest.useFakeTimers();
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      const { unmount } = render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      unmount();

      // Assert
      expect(clearTimeoutSpy).toHaveBeenCalled();

      jest.useRealTimers();
      clearTimeoutSpy.mockRestore();
    });
  });

  describe('useAuth Integration', () => {
    test('should call useAuth hook', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      expect(mockUseAuth).toHaveBeenCalled();
    });

    test('should handle useAuth returning different user states', async () => {
      // Test with null user
      mockUseAuth.mockReturnValue({ user: null });
      const { rerender } = render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      // Reset mock
      mockNavigate.mockClear();

      // Test with valid user
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });
      
      rerender(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });
  });

  describe('Component Lifecycle', () => {
    test('should re-run effect when user state changes', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      const { rerender } = render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      // Change user state
      mockNavigate.mockClear();
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });

      rerender(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    test('should handle multiple rapid re-renders correctly', async () => {
      // Arrange
      jest.useFakeTimers();
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      const { rerender } = render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Rapid re-renders
      rerender(<BrowserRouter><RootRedirect /></BrowserRouter>);
      rerender(<BrowserRouter><RootRedirect /></BrowserRouter>);

      // Fast-forward time
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      jest.useRealTimers();
    });
  });
});
