import React from 'react';
import { render, screen } from '@testing-library/react';
import App from '@/App';

// Mock all the page components
jest.mock('@/pages/SignIn', () => {
  return function MockSignIn() {
    return <div data-testid="signin-page">SignIn Page</div>;
  };
});

jest.mock('@/pages/SignUp', () => {
  return function MockSignUp() {
    return <div data-testid="signup-page">SignUp Page</div>;
  };
});

jest.mock('@/pages/ForgotPassword', () => {
  return function MockForgotPassword() {
    return <div data-testid="forgot-password-page">ForgotPassword Page</div>;
  };
});

jest.mock('@/pages/NotFound', () => {
  return function MockNotFound() {
    return <div data-testid="notfound-page">NotFound Page</div>;
  };
});

jest.mock('@/pages/CreatePassword', () => {
  return function MockCreatePassword() {
    return <div data-testid="create-password-page">CreatePassword Page</div>;
  };
});

jest.mock('@/pages/ResetPassword', () => {
  return function MockResetPassword() {
    return <div data-testid="reset-password-page">ResetPassword Page</div>;
  };
});

jest.mock('@/pages/Profile', () => {
  return function MockProfile() {
    return <div data-testid="profile-page">Profile Page</div>;
  };
});

jest.mock('@/pages/OAuth2Redirect', () => {
  return function MockOAuth2Redirect() {
    return <div data-testid="oauth2-redirect-page">OAuth2Redirect Page</div>;
  };
});

jest.mock('@/components/UserDashboard', () => {
  return function MockUserDashboard() {
    return <div data-testid="user-dashboard">UserDashboard Component</div>;
  };
});

jest.mock('@/components/RootRedirect', () => {
  return function MockRootRedirect() {
    return <div data-testid="root-redirect">RootRedirect Component</div>;
  };
});

jest.mock('@/components/ProtectedRoute', () => {
  return function MockProtectedRoute({ children }) {
    return <div data-testid="protected-route">{children}</div>;
  };
});

// Mock UI components
jest.mock('@/components/ui/toaster', () => ({
  Toaster: () => <div data-testid="toaster" />
}));

jest.mock('@/components/ui/sonner', () => ({
  Toaster: () => <div data-testid="sonner" />
}));

jest.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }) => <div data-testid="tooltip-provider">{children}</div>
}));

// Mock React Query
jest.mock('@tanstack/react-query', () => ({
  QueryClient: jest.fn(() => ({})),
  QueryClientProvider: ({ children }) => <div data-testid="query-client-provider">{children}</div>
}));

// Mock AuthContext
jest.mock('@/contexts/AuthContext', () => ({
  AuthProvider: ({ children }) => <div data-testid="auth-provider">{children}</div>
}));

describe('App Component', () => {
  const renderApp = () => {
    // Since App.jsx already contains BrowserRouter, we don't need to wrap it again
    return render(<App />);
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Provider Setup', () => {
    it('renders with all required providers', () => {
      renderApp();
      
      expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
      expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
      expect(screen.getByTestId('toaster')).toBeInTheDocument();
      expect(screen.getByTestId('sonner')).toBeInTheDocument();
    });
  });

  describe('Route Navigation', () => {
    it('renders RootRedirect component for root path', () => {
      renderApp();
      expect(screen.getByTestId('root-redirect')).toBeInTheDocument();
    });

    it('has proper component structure', () => {
      renderApp();

      // Check that all providers are properly nested
      const queryProvider = screen.getByTestId('query-client-provider');
      const tooltipProvider = screen.getByTestId('tooltip-provider');
      const authProvider = screen.getByTestId('auth-provider');

      expect(queryProvider).toBeInTheDocument();
      expect(tooltipProvider).toBeInTheDocument();
      expect(authProvider).toBeInTheDocument();
    });

    it('renders toaster components', () => {
      renderApp();

      expect(screen.getByTestId('toaster')).toBeInTheDocument();
      expect(screen.getByTestId('sonner')).toBeInTheDocument();
    });

    it('renders without crashing', () => {
      expect(() => {
        renderApp();
      }).not.toThrow();
    });

    it('has proper app structure', () => {
      const { container } = renderApp();

      expect(container.firstChild).toBeInTheDocument();
    });

    it('integrates all providers correctly', () => {
      renderApp();

      // Verify all main providers are present
      expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
      expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
      expect(screen.getByTestId('toaster')).toBeInTheDocument();
      expect(screen.getByTestId('sonner')).toBeInTheDocument();
    });

    it('renders main application content', () => {
      renderApp();

      // The app should render some content (RootRedirect in this case)
      expect(screen.getByTestId('root-redirect')).toBeInTheDocument();
    });

    it('has proper React structure', () => {
      const { container } = renderApp();

      // Should have a single root div
      expect(container.children).toHaveLength(1);
    });
  });

  describe('Component Integration', () => {
    it('renders all required UI components', () => {
      renderApp();

      // Check for all the main UI components
      expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
      expect(screen.getByTestId('toaster')).toBeInTheDocument();
      expect(screen.getByTestId('sonner')).toBeInTheDocument();
      expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
      expect(screen.getByTestId('root-redirect')).toBeInTheDocument();
    });

    it('maintains component hierarchy', () => {
      renderApp();

      // Verify the component hierarchy is maintained
      const queryProvider = screen.getByTestId('query-client-provider');
      const tooltipProvider = screen.getByTestId('tooltip-provider');
      const authProvider = screen.getByTestId('auth-provider');

      expect(queryProvider).toContainElement(tooltipProvider);
      expect(tooltipProvider).toContainElement(authProvider);
    });

    it('renders application successfully', () => {
      const { container } = renderApp();

      expect(container).toBeInTheDocument();
      expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
    });

    it('creates QueryClient instance', () => {
      renderApp();

      // QueryClient is created internally, just verify the provider is rendered
      expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
    });

    it('handles app initialization', () => {
      renderApp();

      // Verify the app initializes properly
      expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
      expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
    });
  });
});
