import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import AuthContainer from '@/components/AuthContainer';

// Mock the child components
jest.mock('@/components/LoginForm', () => {
  return function MockLoginForm({ onSwitchToSignUp, onSwitchToForgotPassword }) {
    return (
      <div data-testid="login-form">
        <h1>Login Form</h1>
        <button onClick={onSwitchToSignUp} data-testid="switch-to-signup">
          Switch to Sign Up
        </button>
        <button onClick={onSwitchToForgotPassword} data-testid="switch-to-forgot-password">
          Switch to Forgot Password
        </button>
      </div>
    );
  };
});

jest.mock('@/components/SignUpForm', () => {
  return function MockSignUpForm({ onSwitchToLogin }) {
    return (
      <div data-testid="signup-form">
        <h1>Sign Up Form</h1>
        <button onClick={onSwitchToLogin} data-testid="switch-to-login">
          Switch to Login
        </button>
      </div>
    );
  };
});

jest.mock('@/components/ForgotPasswordForm', () => {
  return function MockForgotPasswordForm({ onSwitchToLogin }) {
    return (
      <div data-testid="forgot-password-form">
        <h1>Forgot Password Form</h1>
        <button onClick={onSwitchToLogin} data-testid="switch-to-login">
          Switch to Login
        </button>
      </div>
    );
  };
});

jest.mock('@/components/UserDashboard', () => {
  return function MockUserDashboard() {
    return (
      <div data-testid="user-dashboard">
        <h1>User Dashboard</h1>
      </div>
    );
  };
});

// Mock AuthContext
const mockUseAuth = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, ...props }) => <div {...props}>{children}</div>,
  Container: ({ children, ...props }) => <div {...props}>{children}</div>,
  Paper: ({ children, ...props }) => <div {...props}>{children}</div>,
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('AuthContainer Real Implementation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({
      user: null,
    });
  });

  const renderWithRouter = (component, initialEntries = ['/']) => {
    return render(
      <MemoryRouter initialEntries={initialEntries}>
        {component}
      </MemoryRouter>
    );
  };

  it('renders login form by default', () => {
    renderWithRouter(<AuthContainer />);
    
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.getByText('Login Form')).toBeInTheDocument();
  });

  it('renders login form when initialView is login', () => {
    renderWithRouter(<AuthContainer initialView="login" />);
    
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.getByText('Login Form')).toBeInTheDocument();
  });

  it('renders signup form when initialView is signup', () => {
    renderWithRouter(<AuthContainer initialView="signup" />);
    
    expect(screen.getByTestId('signup-form')).toBeInTheDocument();
    expect(screen.getByText('Sign Up Form')).toBeInTheDocument();
  });

  it('renders forgot password form when initialView is forgot-password', () => {
    renderWithRouter(<AuthContainer initialView="forgot-password" />);
    
    expect(screen.getByTestId('forgot-password-form')).toBeInTheDocument();
    expect(screen.getByText('Forgot Password Form')).toBeInTheDocument();
  });

  it('renders user dashboard when user is logged in', () => {
    mockUseAuth.mockReturnValue({
      user: { id: 1, email: '<EMAIL>' },
    });

    renderWithRouter(<AuthContainer />);
    
    expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
    expect(screen.getByText('User Dashboard')).toBeInTheDocument();
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
  });

  it('navigates to signup when switch to signup is clicked from login', async () => {
    const user = userEvent.setup();
    renderWithRouter(<AuthContainer initialView="login" />);
    
    const switchButton = screen.getByTestId('switch-to-signup');
    await user.click(switchButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/signup');
  });

  it('navigates to forgot password when switch to forgot password is clicked from login', async () => {
    const user = userEvent.setup();
    renderWithRouter(<AuthContainer initialView="login" />);
    
    const switchButton = screen.getByTestId('switch-to-forgot-password');
    await user.click(switchButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/forgot-password');
  });

  it('navigates to signin when switch to login is clicked from signup', async () => {
    const user = userEvent.setup();
    renderWithRouter(<AuthContainer initialView="signup" />);
    
    const switchButton = screen.getByTestId('switch-to-login');
    await user.click(switchButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/signin');
  });

  it('navigates to signin when switch to login is clicked from forgot password', async () => {
    const user = userEvent.setup();
    renderWithRouter(<AuthContainer initialView="forgot-password" />);
    
    const switchButton = screen.getByTestId('switch-to-login');
    await user.click(switchButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/signin');
  });

  it('updates view when initialView prop changes', () => {
    const { rerender } = renderWithRouter(<AuthContainer initialView="login" />);
    
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    
    rerender(
      <MemoryRouter>
        <AuthContainer initialView="signup" />
      </MemoryRouter>
    );
    
    expect(screen.getByTestId('signup-form')).toBeInTheDocument();
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
  });

  it('handles invalid initialView gracefully', () => {
    renderWithRouter(<AuthContainer initialView="invalid-view" />);
    
    // Should render nothing for invalid view
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
    expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
    expect(screen.queryByTestId('forgot-password-form')).not.toBeInTheDocument();
  });

  it('maintains user dashboard when user is logged in regardless of initialView', () => {
    mockUseAuth.mockReturnValue({
      user: { id: 1, email: '<EMAIL>' },
    });

    renderWithRouter(<AuthContainer initialView="signup" />);
    
    expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
    expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
  });

  it('passes correct navigation functions to child components', () => {
    renderWithRouter(<AuthContainer initialView="login" />);
    
    // Verify LoginForm receives the correct navigation functions
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.getByTestId('switch-to-signup')).toBeInTheDocument();
    expect(screen.getByTestId('switch-to-forgot-password')).toBeInTheDocument();
  });

  it('renders with proper container structure', () => {
    const { container } = renderWithRouter(<AuthContainer />);
    
    // Should have the main container structure
    expect(container.firstChild).toBeInTheDocument();
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
  });

  it('handles user state changes correctly', () => {
    // Start with no user
    const { rerender } = renderWithRouter(<AuthContainer />);
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    
    // Update to have a user
    mockUseAuth.mockReturnValue({
      user: { id: 1, email: '<EMAIL>' },
    });
    
    rerender(
      <MemoryRouter>
        <AuthContainer />
      </MemoryRouter>
    );
    
    expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
  });
});
