import React from 'react';
import { render, screen } from '@testing-library/react';
import ConfirmationPage from '@/components/ConfirmationPage';

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx, ...props }) => (
    <div data-testid="mui-box" data-sx={JSON.stringify(sx)} {...props}>
      {children}
    </div>
  ),
  Typography: ({ children, variant, sx, ...props }) => (
    <div 
      data-testid={`typography-${variant}`}
      data-variant={variant}
      data-sx={JSON.stringify(sx)}
      {...props}
    >
      {children}
    </div>
  )
}));

describe('ConfirmationPage', () => {
  const renderComponent = () => {
    return render(<ConfirmationPage />);
  };

  describe('Initial Render', () => {
    it('renders the confirmation page', () => {
      renderComponent();
      
      expect(screen.getByTestId('mui-box')).toBeInTheDocument();
    });

    it('displays the main heading', () => {
      renderComponent();
      
      const heading = screen.getByTestId('typography-h5');
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent('Check Your Email');
    });

    it('displays the confirmation message', () => {
      renderComponent();
      
      const message = screen.getByTestId('typography-body1');
      expect(message).toBeInTheDocument();
      expect(message).toHaveTextContent(
        'Activation link has been sent to your email. Please check your inbox to verify your account.'
      );
    });
  });

  describe('Component Structure', () => {
    it('renders heading with correct variant', () => {
      renderComponent();
      
      const heading = screen.getByTestId('typography-h5');
      expect(heading).toHaveAttribute('data-variant', 'h5');
    });

    it('renders message with correct variant', () => {
      renderComponent();
      
      const message = screen.getByTestId('typography-body1');
      expect(message).toHaveAttribute('data-variant', 'body1');
    });

    it('applies correct styling to container', () => {
      renderComponent();
      
      const container = screen.getByTestId('mui-box');
      const sx = JSON.parse(container.getAttribute('data-sx'));
      
      expect(sx).toEqual({
        width: '100%',
        textAlign: 'center',
        mt: 6
      });
    });

    it('applies correct styling to heading', () => {
      renderComponent();
      
      const heading = screen.getByTestId('typography-h5');
      const sx = JSON.parse(heading.getAttribute('data-sx'));
      
      expect(sx).toEqual({
        fontWeight: 700,
        mb: 2
      });
    });

    it('applies correct styling to message', () => {
      renderComponent();
      
      const message = screen.getByTestId('typography-body1');
      const sx = JSON.parse(message.getAttribute('data-sx'));
      
      expect(sx).toEqual({
        color: '#333',
        mb: 2
      });
    });
  });

  describe('Content Verification', () => {
    it('contains expected text content', () => {
      renderComponent();
      
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      expect(screen.getByText(/Activation link has been sent to your email/)).toBeInTheDocument();
      expect(screen.getByText(/Please check your inbox to verify your account/)).toBeInTheDocument();
    });

    it('displays complete confirmation message', () => {
      renderComponent();
      
      const fullMessage = 'Activation link has been sent to your email. Please check your inbox to verify your account.';
      expect(screen.getByText(fullMessage)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      renderComponent();
      
      const heading = screen.getByTestId('typography-h5');
      expect(heading).toHaveAttribute('data-variant', 'h5');
    });

    it('provides clear and informative content', () => {
      renderComponent();
      
      // Check that the content is descriptive and actionable
      expect(screen.getByText(/check your email/i)).toBeInTheDocument();
      expect(screen.getByText(/activation link/i)).toBeInTheDocument();
      expect(screen.getByText(/verify your account/i)).toBeInTheDocument();
    });

    it('uses semantic text elements', () => {
      renderComponent();
      
      // Verify that Typography components are used for text content
      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
      expect(screen.getByTestId('typography-body1')).toBeInTheDocument();
    });
  });

  describe('Layout and Styling', () => {
    it('centers content horizontally', () => {
      renderComponent();
      
      const container = screen.getByTestId('mui-box');
      const sx = JSON.parse(container.getAttribute('data-sx'));
      
      expect(sx.textAlign).toBe('center');
    });

    it('takes full width', () => {
      renderComponent();
      
      const container = screen.getByTestId('mui-box');
      const sx = JSON.parse(container.getAttribute('data-sx'));
      
      expect(sx.width).toBe('100%');
    });

    it('has proper spacing', () => {
      renderComponent();
      
      const container = screen.getByTestId('mui-box');
      const containerSx = JSON.parse(container.getAttribute('data-sx'));
      
      const heading = screen.getByTestId('typography-h5');
      const headingSx = JSON.parse(heading.getAttribute('data-sx'));
      
      const message = screen.getByTestId('typography-body1');
      const messageSx = JSON.parse(message.getAttribute('data-sx'));
      
      expect(containerSx.mt).toBe(6);
      expect(headingSx.mb).toBe(2);
      expect(messageSx.mb).toBe(2);
    });
  });

  describe('Component Behavior', () => {
    it('is a functional component', () => {
      // Verify it renders without props
      expect(() => renderComponent()).not.toThrow();
    });

    it('renders consistently', () => {
      const { unmount } = renderComponent();
      
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      
      unmount();
      
      // Render again to ensure consistency
      renderComponent();
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
    });

    it('does not require any props', () => {
      // Component should render without any props
      const { container } = render(<ConfirmationPage />);
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Text Content Variations', () => {
    it('displays exact heading text', () => {
      renderComponent();
      
      const heading = screen.getByTestId('typography-h5');
      expect(heading.textContent).toBe('Check Your Email');
    });

    it('displays exact message text', () => {
      renderComponent();
      
      const message = screen.getByTestId('typography-body1');
      expect(message.textContent).toBe(
        'Activation link has been sent to your email. Please check your inbox to verify your account.'
      );
    });

    it('contains key instructional words', () => {
      renderComponent();
      
      const text = screen.getByTestId('mui-box').textContent;
      
      expect(text).toMatch(/check/i);
      expect(text).toMatch(/email/i);
      expect(text).toMatch(/activation/i);
      expect(text).toMatch(/verify/i);
      expect(text).toMatch(/account/i);
    });
  });

  describe('Component Integration', () => {
    it('integrates with Material-UI theme system', () => {
      renderComponent();
      
      // Verify that Material-UI components are used
      expect(screen.getByTestId('mui-box')).toBeInTheDocument();
      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
      expect(screen.getByTestId('typography-body1')).toBeInTheDocument();
    });

    it('uses consistent styling approach', () => {
      renderComponent();
      
      // All components should use sx prop for styling
      const container = screen.getByTestId('mui-box');
      const heading = screen.getByTestId('typography-h5');
      const message = screen.getByTestId('typography-body1');
      
      expect(container).toHaveAttribute('data-sx');
      expect(heading).toHaveAttribute('data-sx');
      expect(message).toHaveAttribute('data-sx');
    });
  });

  describe('Error Handling', () => {
    it('handles rendering without errors', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      renderComponent();
      
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('does not throw when rendered multiple times', () => {
      expect(() => {
        renderComponent();
        renderComponent();
        renderComponent();
      }).not.toThrow();
    });
  });
});
