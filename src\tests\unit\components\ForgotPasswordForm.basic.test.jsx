import React from 'react';
import { render, screen } from '@testing-library/react';
import ForgotPasswordForm from '@/components/ForgotPasswordForm';

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Box: ({ children }) => <div data-testid="mui-box">{children}</div>,
  TextField: ({ label, ...props }) => (
    <input data-testid="email-input" placeholder={label} {...props} />
  ),
  Button: ({ children, ...props }) => (
    <button data-testid="submit-button" {...props}>{children}</button>
  ),
  Typography: ({ children }) => <div data-testid="typography">{children}</div>,
  Link: ({ children, ...props }) => (
    <a data-testid="link" {...props}>{children}</a>
  ),
  InputAdornment: ({ children }) => <span>{children}</span>,
  CircularProgress: () => <div data-testid="loading-spinner" />,
  Alert: ({ children }) => <div data-testid="alert">{children}</div>
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Mail: () => <span>📧</span>
}));

// Mock axios
jest.mock('axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: { ip: '***********' } })),
  post: jest.fn(() => Promise.resolve({ status: 200 }))
}));

// Mock environment config
jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080'
}));

// Mock validation messages
jest.mock('@/constants/validationMessages', () => ({
  EMAIL_MESSAGES: {
    REQUIRED: 'Email is required',
    INVALID: 'Please enter a valid email address'
  },
  SUCCESS_MESSAGES: {
    EMAIL_SENT: 'Email sent successfully'
  },
  ERROR_MESSAGES: {
    NETWORK_ERROR: 'Network error occurred'
  },
  BUTTON_TEXT: {
    TRY_AGAIN: 'Try Again',
    SEND_RESET_LINK: 'Send Reset Link'
  },
  FORM_LABELS: {
    EMAIL: 'Email Address'
  },
  REGEX_PATTERNS: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  }
}));

describe('ForgotPasswordForm Basic Tests', () => {
  const mockOnSwitchToLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
  };

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('renders the main container', () => {
      renderComponent();
      expect(screen.getByTestId('mui-box')).toBeInTheDocument();
    });

    it('renders email input', () => {
      renderComponent();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
    });

    it('renders submit button', () => {
      renderComponent();
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
    });

    it('displays form title', () => {
      renderComponent();
      expect(screen.getByText('Forgot Password')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('has email input with email type', () => {
      renderComponent();
      const emailInput = screen.getByTestId('email-input');
      expect(emailInput).toHaveAttribute('type', 'email');
    });

    it('has submit button with submit type', () => {
      renderComponent();
      const submitButton = screen.getByTestId('submit-button');
      expect(submitButton).toHaveAttribute('type', 'submit');
    });

    it('displays mail icon', () => {
      renderComponent();
      // The mail icon is rendered but may not be visible as text
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
    });
  });

  describe('Text Content', () => {
    it('displays form description', () => {
      renderComponent();
      expect(screen.getByText(/Enter your email address/)).toBeInTheDocument();
    });

    it('displays back to login text', () => {
      renderComponent();
      expect(screen.getByText(/Remember your password/)).toBeInTheDocument();
    });

    it('has correct button text', () => {
      renderComponent();
      expect(screen.getByText('Send Reset Link')).toBeInTheDocument();
    });
  });

  describe('Props Handling', () => {
    it('accepts onSwitchToLogin prop', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('renders with minimal props', () => {
      const { container } = render(<ForgotPasswordForm />);
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper form elements', () => {
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      expect(emailInput).toHaveAttribute('type', 'email');
      
      const submitButton = screen.getByTestId('submit-button');
      expect(submitButton).toHaveAttribute('type', 'submit');
    });

    it('provides navigation link', () => {
      renderComponent();
      expect(screen.getByTestId('link')).toBeInTheDocument();
    });
  });
});
