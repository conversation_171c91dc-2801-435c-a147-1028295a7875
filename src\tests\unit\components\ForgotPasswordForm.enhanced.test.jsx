import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ForgotPasswordForm from '../../../components/ForgotPasswordForm';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock environment config
jest.mock('../../../config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:3000'
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, onSubmit, ...props }) => {
    const Element = component || 'div';
    return <Element onSubmit={onSubmit} data-testid="forgot-password-form" {...props}>{children}</Element>;
  },
  TextField: ({ value, onChange, error, helperText, disabled, label, InputProps, ...props }) => (
    <div>
      <input
        data-testid="email-input"
        type="email"
        value={value}
        onChange={onChange}
        disabled={disabled}
        placeholder={label}
        aria-invalid={error}
        aria-describedby={helperText}
        {...props}
      />
      {InputProps?.startAdornment && InputProps.startAdornment}
      {helperText && <div data-testid="helper-text">{helperText}</div>}
    </div>
  ),
  Button: ({ children, onClick, disabled, variant, ...props }) => (
    <button
      data-testid={variant === 'outlined' ? 'try-again-button' : 'submit-button'}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  ),
  Typography: ({ children, variant, component, ...props }) => {
    const Element = component || 'div';
    const testId = variant === 'h5' ? 'typography-h5' :
                   variant === 'body2' && children.includes('Enter your email') ? 'typography-description' :
                   variant === 'body2' && children.includes('Remember your password') ? 'typography-footer' :
                   `typography-${variant}`;
    return <Element data-testid={testId} {...props}>{children}</Element>;
  },
  Link: ({ children, onClick, ...props }) => (
    <button onClick={onClick} data-testid="back-to-signin-link" {...props}>
      {children}
    </button>
  ),
  InputAdornment: ({ children, ...props }) => <div data-testid="input-adornment" {...props}>{children}</div>,
  CircularProgress: ({ ...props }) => <div data-testid="loading-spinner" {...props}>Loading...</div>,
  Alert: ({ children, severity, ...props }) => (
    <div data-testid="alert" data-severity={severity} {...props}>
      {children}
    </div>
  )
}));

// Mock lucide-react
jest.mock('lucide-react', () => ({
  Mail: ({ size, color, ...props }) => <div data-testid="mail-icon" {...props}>Mail</div>
}));

// Mock constants
jest.mock('../../../constants/validationMessages', () => ({
  EMAIL_MESSAGES: {
    REQUIRED: 'Email is required',
    INVALID: 'Please enter a valid email address'
  },
  SUCCESS_MESSAGES: {
    PASSWORD_RESET_SENT: 'Password reset link sent'
  },
  ERROR_MESSAGES: {
    NETWORK_ERROR: 'Network error occurred'
  },
  BUTTON_TEXT: {
    SEND_RESET_LINK: 'Send Reset Link',
    TRY_AGAIN: 'Try Again'
  },
  FORM_LABELS: {
    EMAIL: 'Email'
  },
  REGEX_PATTERNS: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  }
}));

describe('ForgotPasswordForm Enhanced Tests', () => {
  const mockOnSwitchToLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockedAxios.get.mockResolvedValue({ data: { ip: '***********' } });
    mockedAxios.post.mockResolvedValue({ data: { success: true } });
    
    // Mock navigator.userAgent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      writable: true
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <ForgotPasswordForm
        onSwitchToLogin={mockOnSwitchToLogin}
        {...props}
      />
    );
  };

  describe('Initial Form Rendering', () => {
    it('renders without crashing', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('renders all form elements', () => {
      renderComponent();
      
      expect(screen.getByTestId('forgot-password-form')).toBeInTheDocument();
      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
      expect(screen.getByTestId('typography-description')).toBeInTheDocument();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
      expect(screen.getByTestId('back-to-signin-link')).toBeInTheDocument();
    });

    it('displays correct form title', () => {
      renderComponent();
      
      expect(screen.getByText('Forgot Password')).toBeInTheDocument();
    });

    it('displays correct form description', () => {
      renderComponent();
      
      expect(screen.getByText("Enter your email address and we'll send you a link to reset your password")).toBeInTheDocument();
    });

    it('displays correct button text', () => {
      renderComponent();
      
      expect(screen.getByText('Send Reset Link')).toBeInTheDocument();
    });

    it('displays back to sign in link', () => {
      renderComponent();
      
      expect(screen.getByText('Back to sign in')).toBeInTheDocument();
    });

    it('renders email input with mail icon', () => {
      renderComponent();

      expect(screen.getByTestId('email-input')).toBeInTheDocument();
      expect(screen.getByTestId('mail-icon')).toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    it('handles email input changes', () => {
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      expect(emailInput.value).toBe('<EMAIL>');
    });

    it('clears email error when user starts typing', () => {
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');
      
      // Submit empty form to trigger validation error
      fireEvent.click(submitButton);
      
      // Start typing to clear error
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      expect(emailInput).not.toHaveAttribute('aria-invalid', 'true');
    });

    it('handles back to sign in link click', () => {
      renderComponent();
      
      const backLink = screen.getByTestId('back-to-signin-link');
      fireEvent.click(backLink);
      
      expect(mockOnSwitchToLogin).toHaveBeenCalledTimes(1);
    });
  });

  describe('Form Validation', () => {
    it('validates required email field', async () => {
      renderComponent();

      const submitButton = screen.getByTestId('submit-button');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('helper-text')).toHaveTextContent('Email is required');
      });
    });

    it('validates email format', async () => {
      renderComponent();

      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');

      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.click(submitButton);

      // Check that form submission was prevented (API not called)
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });

    it('accepts valid email format', () => {
      renderComponent();

      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);

      expect(screen.queryByTestId('helper-text')).not.toBeInTheDocument();
    });

    it('prevents form submission with invalid data', () => {
      renderComponent();
      
      const submitButton = screen.getByTestId('submit-button');
      fireEvent.click(submitButton);
      
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });
  });

  describe('Form Submission', () => {
    it('submits form with valid email', async () => {
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('https://api.ipify.org?format=json');
      });
      
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:3000/api/v1/forgot-password',
          {
            email: '<EMAIL>',
            ipAddress: '***********',
            deviceDetails: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          },
          {
            headers: { 'Content-Type': 'application/json' },
            validateStatus: expect.any(Function)
          }
        );
      });
    });

    it('shows loading state during submission', async () => {
      // Mock a delayed response
      mockedAxios.post.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);
      
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      expect(emailInput).toBeDisabled();
    });

    it('handles IP address fetch failure gracefully', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network error'));
      
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:3000/api/v1/forgot-password',
          {
            email: '<EMAIL>',
            ipAddress: '',
            deviceDetails: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          },
          expect.any(Object)
        );
      });
    });

    it('handles API error gracefully', async () => {
      mockedAxios.post.mockRejectedValue(new Error('API error'));
      
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      });
    });
  });

  describe('Success State', () => {
    beforeEach(async () => {
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const submitButton = screen.getByTestId('submit-button');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      });
    });

    it('displays success message', () => {
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      expect(screen.getByText('Password reset request submitted')).toBeInTheDocument();
    });

    it('displays security alert message', () => {
      expect(screen.getByTestId('alert')).toBeInTheDocument();
      expect(screen.getByText(/If the email exists in our system/)).toBeInTheDocument();
    });

    it('displays try again button', () => {
      expect(screen.getByTestId('try-again-button')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('displays back to sign in link', () => {
      expect(screen.getByTestId('back-to-signin-link')).toBeInTheDocument();
      expect(screen.getByText('Back to sign in')).toBeInTheDocument();
    });

    it('handles try again button click', () => {
      const tryAgainButton = screen.getByTestId('try-again-button');
      fireEvent.click(tryAgainButton);
      
      // Should return to the form
      expect(screen.getByText('Forgot Password')).toBeInTheDocument();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
    });

    it('handles back to sign in link click in success state', () => {
      const backLink = screen.getByTestId('back-to-signin-link');
      fireEvent.click(backLink);
      
      expect(mockOnSwitchToLogin).toHaveBeenCalledTimes(1);
    });
  });
});
