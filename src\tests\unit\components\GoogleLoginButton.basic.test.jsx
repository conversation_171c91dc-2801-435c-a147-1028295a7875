import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock the GoogleLoginButton component to avoid import.meta issues
const MockGoogleLoginButton = () => {
  const { isLoading } = mockUseAuth;

  return (
    <div data-testid="mui-box">
      <button data-testid="google-login-button" disabled={isLoading}>
        <svg role="img" width="18" height="18" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Continue with Google
      </button>
    </div>
  );
};

// Mock the AuthContext
const mockUseAuth = {
  isLoading: false
};

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Button: ({ children, onClick, disabled, ...props }) => (
    <button
      data-testid="google-login-button"
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  ),
  Box: ({ children }) => (
    <div data-testid="mui-box">{children}</div>
  )
}));

// Mock environment variables
process.env.VITE_APP_GOOGLE_OAUTH_URL = 'https://accounts.google.com/oauth/authorize?client_id=test';

describe('GoogleLoginButton Basic Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.isLoading = false;
    
    // Mock window.location.href
    delete window.location;
    window.location = { href: '' };
  });

  const renderComponent = () => {
    return render(<MockGoogleLoginButton />);
  };

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('renders the Google login button', () => {
      renderComponent();
      expect(screen.getByTestId('google-login-button')).toBeInTheDocument();
    });

    it('displays correct button text', () => {
      renderComponent();
      expect(screen.getByText('Continue with Google')).toBeInTheDocument();
    });

    it('renders container box', () => {
      renderComponent();
      expect(screen.getByTestId('mui-box')).toBeInTheDocument();
    });
  });

  describe('Button Properties', () => {
    it('is enabled by default', () => {
      renderComponent();
      const button = screen.getByTestId('google-login-button');
      expect(button).not.toBeDisabled();
    });

    it('has proper button role', () => {
      renderComponent();
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('contains Google branding text', () => {
      renderComponent();
      expect(screen.getByText(/Continue with Google/i)).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('is disabled when auth is loading', () => {
      mockUseAuth.isLoading = true;
      renderComponent();
      
      const button = screen.getByTestId('google-login-button');
      expect(button).toBeDisabled();
    });

    it('is enabled when not loading', () => {
      mockUseAuth.isLoading = false;
      renderComponent();
      
      const button = screen.getByTestId('google-login-button');
      expect(button).not.toBeDisabled();
    });
  });

  describe('Component Structure', () => {
    it('has proper component hierarchy', () => {
      renderComponent();
      
      const container = screen.getByTestId('mui-box');
      const button = screen.getByTestId('google-login-button');
      
      expect(container).toBeInTheDocument();
      expect(button).toBeInTheDocument();
    });

    it('renders Google icon SVG', () => {
      renderComponent();
      
      // Check for SVG element
      const svg = screen.getByRole('img', { hidden: true });
      expect(svg).toBeInTheDocument();
      expect(svg.tagName).toBe('svg');
    });
  });

  describe('Accessibility', () => {
    it('has accessible button text', () => {
      renderComponent();
      
      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).toBeInTheDocument();
    });

    it('provides visual feedback for disabled state', () => {
      mockUseAuth.isLoading = true;
      renderComponent();
      
      const button = screen.getByTestId('google-login-button');
      expect(button).toBeDisabled();
    });
  });

  describe('Visual Elements', () => {
    it('contains Google icon with correct attributes', () => {
      renderComponent();
      
      const svg = screen.getByRole('img', { hidden: true });
      expect(svg).toHaveAttribute('width', '18');
      expect(svg).toHaveAttribute('height', '18');
      expect(svg).toHaveAttribute('viewBox', '0 0 24 24');
    });

    it('has Google brand colors in SVG paths', () => {
      renderComponent();
      
      const svg = screen.getByRole('img', { hidden: true });
      const paths = svg.querySelectorAll('path');
      
      expect(paths).toHaveLength(4);
      
      // Check for Google brand colors
      const fills = Array.from(paths).map(path => path.getAttribute('fill'));
      expect(fills).toContain('#4285F4'); // Blue
      expect(fills).toContain('#34A853'); // Green
      expect(fills).toContain('#FBBC05'); // Yellow
      expect(fills).toContain('#EA4335'); // Red
    });
  });

  describe('Error Handling', () => {
    it('handles rendering without errors', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      renderComponent();
      
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('renders consistently multiple times', () => {
      const { unmount } = renderComponent();
      expect(screen.getByText('Continue with Google')).toBeInTheDocument();
      
      unmount();
      
      renderComponent();
      expect(screen.getByText('Continue with Google')).toBeInTheDocument();
    });
  });

  describe('Integration', () => {
    it('integrates with auth context properly', () => {
      // Test different loading states
      mockUseAuth.isLoading = false;
      const { rerender } = renderComponent();

      let button = screen.getByTestId('google-login-button');
      expect(button).not.toBeDisabled();

      mockUseAuth.isLoading = true;
      rerender(<MockGoogleLoginButton />);

      button = screen.getByTestId('google-login-button');
      expect(button).toBeDisabled();
    });

    it('uses Material-UI components', () => {
      renderComponent();
      
      expect(screen.getByTestId('mui-box')).toBeInTheDocument();
      expect(screen.getByTestId('google-login-button')).toBeInTheDocument();
    });
  });

  describe('Props and Configuration', () => {
    it('renders without requiring props', () => {
      expect(() => {
        render(<MockGoogleLoginButton />);
      }).not.toThrow();
    });

    it('maintains consistent appearance', () => {
      renderComponent();
      
      const button = screen.getByTestId('google-login-button');
      expect(button).toHaveTextContent('Continue with Google');
      
      const svg = screen.getByRole('img', { hidden: true });
      expect(svg).toBeInTheDocument();
    });
  });
});
