import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import LoginForm from '../../../components/LoginForm';

// Mock the useAuth hook
const mockUseAuth = {
  login: jest.fn(),
  isLoading: false
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth
}));

// Mock the hooks
const mockFormValidation = {
  values: { email: '', password: '' },
  errors: {},
  handleInputChange: jest.fn(),
  handleBlur: jest.fn(),
  validateForm: jest.fn(() => true)
};

const mockApiCall = {
  execute: jest.fn(),
  loading: false,
  error: null
};

jest.mock('../../../hooks', () => ({
  useFormValidation: () => mockFormValidation,
  useApiCall: () => mockApiCall
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, onSubmit, sx, ...props }) => {
    const Element = component || 'div';
    const testId = sx?.textAlign === 'right' ? 'forgot-password-container' : 'login-form-box';
    return <Element onSubmit={onSubmit} data-testid={testId} {...props}>{children}</Element>;
  },
  Link: ({ children, onClick, ...props }) => (
    <button onClick={onClick} data-testid="forgot-password-link" {...props}>
      {children}
    </button>
  )
}));

// Mock common components
jest.mock('../../../components/common', () => ({
  EmailInput: ({ value, onChange, onBlur, error, helperText, disabled, label, ...props }) => (
    <input
      data-testid="email-input"
      type="email"
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      disabled={disabled}
      placeholder={label}
      aria-invalid={error}
      aria-describedby={helperText}
      {...props}
    />
  ),
  PasswordInput: ({ value, onChange, onBlur, error, helperText, disabled, label, ...props }) => (
    <input
      data-testid="password-input"
      type="password"
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      disabled={disabled}
      placeholder={label}
      aria-invalid={error}
      aria-describedby={helperText}
      {...props}
    />
  ),
  SubmitButton: ({ children, disabled, loading, ...props }) => (
    <button
      data-testid="submit-button"
      type="submit"
      disabled={disabled || loading}
      {...props}
    >
      {loading ? 'Loading...' : children}
    </button>
  ),
  AlertMessage: ({ message, severity, show, ...props }) => (
    show ? (
      <div data-testid="alert-message" data-severity={severity} {...props}>
        {message}
      </div>
    ) : null
  ),
  AuthFormHeader: ({ title, subtitle, showLogo, ...props }) => (
    <div data-testid="auth-form-header" {...props}>
      <h1>{title}</h1>
      <p>{subtitle}</p>
      {showLogo && <div data-testid="logo">Logo</div>}
    </div>
  ),
  AuthFormFooter: ({ message, linkText, onLinkClick, ...props }) => (
    <div data-testid="auth-form-footer" {...props}>
      <span>{message}</span>
      <button onClick={onLinkClick} data-testid="footer-link">
        {linkText}
      </button>
    </div>
  )
}));

// Mock constants
jest.mock('../../../constants/validationMessages', () => ({
  SUCCESS_MESSAGES: {
    LOGIN_SUCCESS: 'Login successful'
  },
  ERROR_MESSAGES: {
    INVALID_CREDENTIALS: 'Invalid credentials'
  },
  BUTTON_TEXT: {
    SIGN_IN: 'Sign In'
  },
  FORM_LABELS: {
    EMAIL: 'Email',
    AUTH_FIELD: 'Password'
  }
}));

describe('LoginForm Enhanced Tests', () => {
  const mockOnSwitchToSignUp = jest.fn();
  const mockOnSwitchToForgotPassword = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.isLoading = false;
    mockUseAuth.login.mockResolvedValue();
    mockFormValidation.values = { email: '', password: '' };
    mockFormValidation.errors = {};
    mockFormValidation.validateForm.mockReturnValue(true);
    mockApiCall.loading = false;
    mockApiCall.error = null;
    mockApiCall.execute.mockResolvedValue();
  });

  const renderComponent = (props = {}) => {
    return render(
      <LoginForm
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        {...props}
      />
    );
  };

  describe('Component Rendering', () => {
    it('renders without crashing', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('renders all form elements', () => {
      renderComponent();
      
      expect(screen.getByTestId('login-form-box')).toBeInTheDocument();
      expect(screen.getByTestId('auth-form-header')).toBeInTheDocument();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
      expect(screen.getByTestId('password-input')).toBeInTheDocument();
      expect(screen.getByTestId('forgot-password-container')).toBeInTheDocument();
      expect(screen.getByTestId('forgot-password-link')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
      expect(screen.getByTestId('auth-form-footer')).toBeInTheDocument();
    });

    it('displays correct header content', () => {
      renderComponent();
      
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      expect(screen.getByText('Sign in to your account to continue')).toBeInTheDocument();
    });

    it('displays correct button text', () => {
      renderComponent();
      
      expect(screen.getByText('Sign In')).toBeInTheDocument();
    });

    it('displays forgot password link', () => {
      renderComponent();
      
      expect(screen.getByText('Forgot password?')).toBeInTheDocument();
    });

    it('displays footer content', () => {
      renderComponent();
      
      expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
      expect(screen.getByText('Sign up')).toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    it('handles email input changes', () => {
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      expect(mockFormValidation.handleInputChange).toHaveBeenCalledWith('email', '<EMAIL>');
    });

    it('handles password input changes', () => {
      renderComponent();
      
      const passwordInput = screen.getByTestId('password-input');
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      
      expect(mockFormValidation.handleInputChange).toHaveBeenCalledWith('password', 'password123');
    });

    it('handles email input blur', () => {
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.blur(emailInput);
      
      expect(mockFormValidation.handleBlur).toHaveBeenCalledWith('email');
    });

    it('handles password input blur', () => {
      renderComponent();
      
      const passwordInput = screen.getByTestId('password-input');
      fireEvent.blur(passwordInput);
      
      expect(mockFormValidation.handleBlur).toHaveBeenCalledWith('password');
    });

    it('handles forgot password link click', () => {
      renderComponent();
      
      const forgotPasswordLink = screen.getByTestId('forgot-password-link');
      fireEvent.click(forgotPasswordLink);
      
      expect(mockOnSwitchToForgotPassword).toHaveBeenCalledTimes(1);
    });

    it('handles sign up link click', () => {
      renderComponent();
      
      const signUpLink = screen.getByTestId('footer-link');
      fireEvent.click(signUpLink);
      
      expect(mockOnSwitchToSignUp).toHaveBeenCalledTimes(1);
    });
  });

  describe('Form Submission', () => {
    it('handles form submission with valid data', async () => {
      mockFormValidation.values = { email: '<EMAIL>', password: 'password123' };
      mockFormValidation.validateForm.mockReturnValue(true);
      
      renderComponent();
      
      const form = screen.getByTestId('login-form-box');
      fireEvent.submit(form);
      
      expect(mockFormValidation.validateForm).toHaveBeenCalledWith(['email', 'password']);
      await waitFor(() => {
        expect(mockApiCall.execute).toHaveBeenCalledTimes(1);
      });
    });

    it('prevents submission with invalid data', async () => {
      mockFormValidation.validateForm.mockReturnValue(false);
      
      renderComponent();
      
      const form = screen.getByTestId('login-form-box');
      fireEvent.submit(form);
      
      expect(mockFormValidation.validateForm).toHaveBeenCalledWith(['email', 'password']);
      expect(mockApiCall.execute).not.toHaveBeenCalled();
    });

    it('calls login function with correct parameters', async () => {
      mockFormValidation.values = { email: '<EMAIL>', password: 'password123' };
      mockFormValidation.validateForm.mockReturnValue(true);
      
      renderComponent();
      
      const form = screen.getByTestId('login-form-box');
      fireEvent.submit(form);
      
      await waitFor(() => {
        expect(mockApiCall.execute).toHaveBeenCalledWith(
          expect.any(Function),
          {
            successMessage: 'Login successful',
            errorMessage: 'Invalid credentials'
          }
        );
      });
    });

    it('prevents default form submission', () => {
      renderComponent();
      
      const form = screen.getByTestId('login-form-box');
      const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
      const preventDefaultSpy = jest.spyOn(submitEvent, 'preventDefault');
      
      form.dispatchEvent(submitEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    it('disables inputs when auth is loading', () => {
      mockUseAuth.isLoading = true;
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const passwordInput = screen.getByTestId('password-input');
      
      expect(emailInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
    });

    it('disables submit button when auth is loading', () => {
      mockUseAuth.isLoading = true;
      renderComponent();
      
      const submitButton = screen.getByTestId('submit-button');
      expect(submitButton).toBeDisabled();
    });

    it('shows loading state on submit button during API call', () => {
      mockApiCall.loading = true;
      renderComponent();
      
      const submitButton = screen.getByTestId('submit-button');
      expect(submitButton).toBeDisabled();
      expect(submitButton).toHaveTextContent('Loading...');
    });

    it('enables form when not loading', () => {
      mockUseAuth.isLoading = false;
      mockApiCall.loading = false;
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const passwordInput = screen.getByTestId('password-input');
      const submitButton = screen.getByTestId('submit-button');
      
      expect(emailInput).not.toBeDisabled();
      expect(passwordInput).not.toBeDisabled();
      expect(submitButton).not.toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('displays API error messages', () => {
      mockApiCall.error = 'Invalid credentials';
      renderComponent();
      
      const alertMessage = screen.getByTestId('alert-message');
      expect(alertMessage).toBeInTheDocument();
      expect(alertMessage).toHaveTextContent('Invalid credentials');
      expect(alertMessage).toHaveAttribute('data-severity', 'error');
    });

    it('hides alert when no error', () => {
      mockApiCall.error = null;
      renderComponent();
      
      const alertMessage = screen.queryByTestId('alert-message');
      expect(alertMessage).not.toBeInTheDocument();
    });

    it('displays form validation errors', () => {
      mockFormValidation.errors = { 
        email: 'Email is required',
        password: 'Password is required'
      };
      renderComponent();
      
      const emailInput = screen.getByTestId('email-input');
      const passwordInput = screen.getByTestId('password-input');
      
      expect(emailInput).toHaveAttribute('aria-invalid', 'true');
      expect(passwordInput).toHaveAttribute('aria-invalid', 'true');
    });
  });
});
