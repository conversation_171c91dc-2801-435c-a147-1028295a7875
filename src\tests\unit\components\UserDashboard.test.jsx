import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'sonner';
import UserDashboard from '@/components/UserDashboard';


// Mock dependencies
jest.mock('axios');
jest.mock('sonner');

// Mock environment configuration
jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080',
}));


// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock AuthContext
const mockUseAuth = jest.fn();
const mockLogout = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, sx, ...props }) => {
    const Component = component || 'div';
    return <Component data-testid="box" {...props}>{children}</Component>;
  },
  Container: ({ children, maxWidth, ...props }) => (
    <div data-testid="container" data-maxwidth={maxWidth} {...props}>{children}</div>
  ),
  Typography: ({ children, variant, color, sx, ...props }) => (
    <div data-testid="typography" data-variant={variant} data-color={color} {...props}>
      {children}
    </div>
  ),
  Avatar: ({ children, src, sx, ...props }) => (
    <div data-testid="avatar" data-src={src} {...props}>{children}</div>
  ),
  Chip: ({ label, color, variant, size, sx, ...props }) => (
    <div data-testid="chip" data-label={label} data-color={color} data-variant={variant} {...props}>
      {label}
    </div>
  ),
  Card: ({ children, sx, ...props }) => (
    <div data-testid="card" {...props}>{children}</div>
  ),
  CardContent: ({ children, sx, ...props }) => (
    <div data-testid="card-content" {...props}>{children}</div>
  ),
  AppBar: ({ children, position, sx, ...props }) => (
    <div data-testid="app-bar" data-position={position} {...props}>{children}</div>
  ),
  Toolbar: ({ children, sx, ...props }) => (
    <div data-testid="toolbar" {...props}>{children}</div>
  ),
  IconButton: ({ children, onClick, sx, color, edge, ...props }) => (
    <button data-testid="icon-button" onClick={onClick} data-color={color} data-edge={edge} {...props}>
      {children}
    </button>
  ),
  Badge: ({ children, badgeContent, color, ...props }) => (
    <div data-testid="badge" data-badge-content={badgeContent} data-color={color} {...props}>
      {children}
    </div>
  ),
  Menu: ({ children, anchorEl, open, onClose, transformOrigin, anchorOrigin, slotProps, ...props }) => (
    open ? <div data-testid="menu" {...props}>{children}</div> : null
  ),
  MenuItem: ({ children, onClick, sx, ...props }) => (
    <div data-testid="menu-item" onClick={onClick} {...props}>{children}</div>
  ),
  Drawer: ({ children, variant, open, onClose, ModalProps, sx, ...props }) => (
    open ? <div data-testid="drawer" data-variant={variant} {...props}>{children}</div> : null
  ),
  List: ({ children, ...props }) => (
    <div data-testid="list" {...props}>{children}</div>
  ),
  ListItem: ({ children, sx, ...props }) => (
    <div data-testid="list-item" {...props}>{children}</div>
  ),
  ListItemIcon: ({ children, sx, ...props }) => (
    <div data-testid="list-item-icon" {...props}>{children}</div>
  ),
  ListItemText: ({ primary, slotProps, ...props }) => (
    <div data-testid="list-item-text" {...props}>{primary}</div>
  ),
  Divider: (props) => <div data-testid="divider" {...props} />,
  useTheme: () => ({
    zIndex: { drawer: 1200 },
    breakpoints: { down: () => false }
  }),
  useMediaQuery: () => false,
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  LogOut: () => <div data-testid="logout-icon" />,
  User: () => <div data-testid="user-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Bell: () => <div data-testid="bell-icon" />,
  Menu: () => <div data-testid="menu-icon" />,
  BarChart3: () => <div data-testid="bar-chart-icon" />,
  Users: () => <div data-testid="users-icon" />,
  FileText: () => <div data-testid="file-text-icon" />,
  HelpCircle: () => <div data-testid="help-circle-icon" />,
  Home: () => <div data-testid="home-icon" />,
  Activity: () => <div data-testid="activity-icon" />,
  Calendar: () => <div data-testid="calendar-icon" />,
  MessageSquare: () => <div data-testid="message-square-icon" />,
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock navigator.userAgent
Object.defineProperty(navigator, 'userAgent', {
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  configurable: true,
});

describe('UserDashboard', () => {
  const mockUser = {
    id: 'user123',
    name: 'John Doe',
    email: '<EMAIL>',
    profilePicture: 'https://example.com/profile.jpg'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({
      user: mockUser,
      logout: mockLogout,
    });
    localStorageMock.getItem.mockReturnValue('mock-session-token');
    axios.get.mockResolvedValue({ data: { ip: '***********' } });
    axios.post.mockResolvedValue({ status: 200 });
  });

  const renderWithRouter = (component) => {
    return render(
      <BrowserRouter>
        {component}
      </BrowserRouter>
    );
  };

  describe('Component Rendering', () => {
    test('should render dashboard with user information', () => {
      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByText('MyApp Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Welcome back, John Doe!')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    test('should render navigation items', () => {
      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByText('Analytics')).toBeInTheDocument();
      expect(screen.getByText('Users')).toBeInTheDocument();
      expect(screen.getByText('Projects')).toBeInTheDocument();
      expect(screen.getByText('Calendar')).toBeInTheDocument();
      expect(screen.getByText('Messages')).toBeInTheDocument();
      expect(screen.getByText('Activity')).toBeInTheDocument();
      expect(screen.getByText('Help')).toBeInTheDocument();
    });

    test('should render dashboard statistics', () => {
      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByText('Total Users')).toBeInTheDocument();
      expect(screen.getByText('1,234')).toBeInTheDocument();
      expect(screen.getByText('Revenue')).toBeInTheDocument();
      expect(screen.getByText('$45,678')).toBeInTheDocument();
      expect(screen.getByText('Active Projects')).toBeInTheDocument();
      expect(screen.getByText('89')).toBeInTheDocument();
      expect(screen.getByText('Tasks Completed')).toBeInTheDocument();
      expect(screen.getByText('156')).toBeInTheDocument();
    });

    test('should render account status cards', () => {
      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByText('Account Status')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('Login Method')).toBeInTheDocument();
      expect(screen.getByText('Security')).toBeInTheDocument();
      expect(screen.getByText('2FA Enabled')).toBeInTheDocument();
    });
  });

  describe('User Authentication Display', () => {
    test('should display Google OAuth for Google users', () => {
      // Arrange
      const googleUser = { ...mockUser, id: 'google_123' };
      mockUseAuth.mockReturnValue({
        user: googleUser,
        logout: mockLogout,
      });

      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByText('Google OAuth')).toBeInTheDocument();
    });

    test('should display Email & Password for regular users', () => {
      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByText('Email & Password')).toBeInTheDocument();
    });

    test('should handle user without profile picture', () => {
      // Arrange
      const userWithoutPicture = { ...mockUser, profilePicture: undefined };
      mockUseAuth.mockReturnValue({
        user: userWithoutPicture,
        logout: mockLogout,
      });

      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByTestId('user-icon')).toBeInTheDocument();
    });
  });

  describe('Notifications', () => {
    test('should display notification badge with unread count', () => {
      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      const badge = screen.getByTestId('badge');
      expect(badge).toHaveAttribute('data-badge-content', '2'); // 2 unread notifications
    });

    test('should open notifications menu when bell icon is clicked', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<UserDashboard />);

      // Act
      const bellButton = screen.getByTestId('bell-icon').closest('button');
      await user.click(bellButton);

      // Assert
      expect(screen.getByText('Notifications')).toBeInTheDocument();
      expect(screen.getByText('New user registered')).toBeInTheDocument();
      expect(screen.getByText('System update completed')).toBeInTheDocument();
    });
  });

  describe('Settings Menu', () => {
    test('should open settings menu when settings icon is clicked', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<UserDashboard />);

      // Act
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      await user.click(settingsButton);

      // Assert
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
      expect(screen.getByText('Sign Out')).toBeInTheDocument();
    });

    test('should navigate to profile when Profile Settings is clicked', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<UserDashboard />);

      // Act
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      await user.click(settingsButton);

      const profileMenuItem = screen.getByText('Profile Settings').closest('[data-testid="menu-item"]');
      await user.click(profileMenuItem);

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith('/profile');
    });
  });

  describe('Logout Functionality', () => {
    test('should handle successful logout', async () => {
      // Arrange
      const user = userEvent.setup();
      axios.post.mockResolvedValue({ status: 200 });
      renderWithRouter(<UserDashboard />);

      // Act
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      await user.click(settingsButton);

      const signOutMenuItem = screen.getByText('Sign Out').closest('[data-testid="menu-item"]');
      await user.click(signOutMenuItem);

      // Assert
      await waitFor(() => {
        expect(axios.get).toHaveBeenCalledWith('https://api.ipify.org?format=json');
        expect(axios.post).toHaveBeenCalledWith(
          'http://localhost:8080/api/v1/logout',
          {
            sessionToken: 'mock-session-token',
            ipAddress: '***********',
            deviceDetails: navigator.userAgent
          },
          {
            headers: {
              'Authorization': 'Bearer mock-session-token',
              'Content-Type': 'application/json'
            },
            validateStatus: expect.any(Function)
          }
        );
        expect(mockLogout).toHaveBeenCalled();
        expect(toast.success).toHaveBeenCalledWith('Logged out successfully');
        expect(mockNavigate).toHaveBeenCalledWith('/');
      });
    });

    test('should handle logout API failure gracefully', async () => {
      // Arrange
      const user = userEvent.setup();
      axios.post.mockRejectedValue(new Error('Network error'));
      renderWithRouter(<UserDashboard />);

      // Act
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      await user.click(settingsButton);

      const signOutMenuItem = screen.getByText('Sign Out').closest('[data-testid="menu-item"]');
      await user.click(signOutMenuItem);

      // Assert
      await waitFor(() => {
        expect(mockLogout).toHaveBeenCalled();
        expect(toast.error).toHaveBeenCalledWith('Logged out successfully');
        expect(mockNavigate).toHaveBeenCalledWith('/');
      });
    });

    test('should clear localStorage on logout', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<UserDashboard />);

      // Act
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      await user.click(settingsButton);

      const signOutMenuItem = screen.getByText('Sign Out').closest('[data-testid="menu-item"]');
      await user.click(signOutMenuItem);

      // Assert
      await waitFor(() => {
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('userId');
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('sessionToken');
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('refreshToken');
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('expiresAt');
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('refreshExpiresAt');
      });
    });

    test('should handle IP address fetch failure during logout', async () => {
      // Arrange
      const user = userEvent.setup();
      axios.get.mockRejectedValue(new Error('IP fetch failed'));
      axios.post.mockResolvedValue({ status: 200 });
      renderWithRouter(<UserDashboard />);

      // Act
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      await user.click(settingsButton);

      const signOutMenuItem = screen.getByText('Sign Out').closest('[data-testid="menu-item"]');
      await user.click(signOutMenuItem);

      // Assert
      await waitFor(() => {
        expect(axios.post).toHaveBeenCalledWith(
          'http://localhost:8080/api/v1/logout',
          expect.objectContaining({
            ipAddress: '', // Should be empty string when IP fetch fails
          }),
          expect.any(Object)
        );
      });
    });
  });

  describe('State Management', () => {
    test('should handle drawer toggle for mobile', async () => {
      // This test would be more meaningful with actual mobile breakpoint testing
      // For now, we test the function exists and can be called
      const user = userEvent.setup();
      renderWithRouter(<UserDashboard />);

      // The drawer toggle functionality is tested implicitly through rendering
      expect(screen.getByTestId('drawer')).toBeInTheDocument();
    });

    test('should close menus when clicking menu items', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<UserDashboard />);

      // Act - Open notifications menu
      const bellButton = screen.getByTestId('bell-icon').closest('button');
      await user.click(bellButton);

      // Verify menu is open
      expect(screen.getByText('New user registered')).toBeInTheDocument();

      // Click on a notification
      const notificationItem = screen.getByText('New user registered').closest('[data-testid="menu-item"]');
      await user.click(notificationItem);

      // The menu should close (this is handled by the onClose prop in the real component)
      // In our mock implementation, the menu closes when clicked, so the item should no longer be visible
      // This test verifies the click handler works correctly
      expect(screen.queryByText('Notifications')).not.toBeInTheDocument();
    });
  });

  describe('Environment Configuration', () => {
    test('should use default API URL when environment variable is not set', () => {
      // Act
      renderWithRouter(<UserDashboard />);

      // This test verifies the component renders correctly
      // The actual API URL usage is tested in the logout functionality tests
      expect(screen.getByText('MyApp Dashboard')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('should handle missing user gracefully', () => {
      // Arrange
      mockUseAuth.mockReturnValue({
        user: null,
        logout: mockLogout,
      });

      // Act
      renderWithRouter(<UserDashboard />);

      // Assert - Should not crash and should handle undefined user
      expect(screen.getByText('Welcome back, !')).toBeInTheDocument();
    });

    test('should handle user without name gracefully', () => {
      // Arrange
      const userWithoutName = { ...mockUser, name: undefined };
      mockUseAuth.mockReturnValue({
        user: userWithoutName,
        logout: mockLogout,
      });

      // Act
      renderWithRouter(<UserDashboard />);

      // Assert
      expect(screen.getByText('Welcome back, !')).toBeInTheDocument();
    });
  });
});
