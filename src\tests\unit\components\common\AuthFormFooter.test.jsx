import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import AuthFormFooter from '@/components/common/AuthFormFooter';

jest.mock('@mui/material', () => ({
  Typography: jest.fn(({ children, ...props }) => <div data-testid="mui-typography" {...props}>{children}</div>),
  Link: jest.fn(({ children, ...props }) => <button data-testid="mui-link" {...props}>{children}</button>),
}));

describe('AuthFormFooter', () => {
  it('renders message and linkText', () => {
    render(<AuthFormFooter message="Test message" linkText="Test Link" onLinkClick={jest.fn()} />);
    expect(screen.getByText('Test message')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Test Link' })).toBeInTheDocument();
  });

  it('calls onLinkClick when link is clicked', () => {
    const onLinkClick = jest.fn();
    render(<AuthFormFooter message="Msg" linkText="ClickMe" onLinkClick={onLinkClick} />);
    fireEvent.click(screen.getByRole('button', { name: 'ClickMe' }));
    expect(onLinkClick).toHaveBeenCalled();
  });

  it('renders with empty message and linkText', () => {
    render(<AuthFormFooter message="" linkText="" onLinkClick={jest.fn()} />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
}); 