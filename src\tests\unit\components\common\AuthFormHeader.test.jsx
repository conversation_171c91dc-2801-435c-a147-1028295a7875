import React from 'react';
import { render, screen } from '@testing-library/react';
import AuthFormHeader from '@/components/common/AuthFormHeader';

jest.mock('@mui/material', () => ({
  Box: jest.fn(({ children, ...props }) => <div data-testid="mui-box" {...props}>{children}</div>),
  Typography: jest.fn(({ children, ...props }) => <div data-testid="mui-typography" {...props}>{children}</div>),
}));
jest.mock('@/components/GoogleLoginButton', () => () => <button data-testid="google-login-btn">Google Login</button>);

describe('AuthFormHeader', () => {
  const requiredProps = {
    title: 'Welcome',
    subtitle: 'Sign in to continue',
  };

  it('renders logo, title, subtitle, Google login, and divider by default', () => {
    render(<AuthFormHeader {...requiredProps} />);
    expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
    expect(screen.getByText('Welcome')).toBeInTheDocument();
    expect(screen.getByText('Sign in to continue')).toBeInTheDocument();
    expect(screen.getByTestId('google-login-btn')).toBeInTheDocument();
    expect(screen.getByText('or continue with email')).toBeInTheDocument();
  });

  it('does not render logo if showLogo is false', () => {
    render(<AuthFormHeader {...requiredProps} showLogo={false} />);
    expect(screen.queryByAltText('Brand Logo')).not.toBeInTheDocument();
  });

  it('does not render Google login or divider if showGoogleLogin is false', () => {
    render(<AuthFormHeader {...requiredProps} showGoogleLogin={false} />);
    expect(screen.queryByTestId('google-login-btn')).not.toBeInTheDocument();
    expect(screen.queryByText('or continue with email')).not.toBeInTheDocument();
  });

  it('renders custom logo src and alt', () => {
    render(<AuthFormHeader {...requiredProps} logoSrc="/custom.svg" logoAlt="Custom Logo" />);
    const img = screen.getByAltText('Custom Logo');
    expect(img).toHaveAttribute('src', '/custom.svg');
  });

  it('renders with empty title and subtitle', () => {
    render(<AuthFormHeader title="" subtitle="" />);
    const typographies = screen.getAllByTestId('mui-typography');
    // The first is the title, the second is the subtitle, the third is the divider text
    expect(typographies[0]).toHaveTextContent('');
    expect(typographies[1]).toHaveTextContent('');
    expect(typographies[2]).toHaveTextContent('or continue with email');
  });
}); 