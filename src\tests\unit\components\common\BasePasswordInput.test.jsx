import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import BasePasswordInput from '@/components/common/BasePasswordInput';

jest.mock('@mui/material', () => ({
  TextField: jest.fn(({ InputProps, ...props }) => (
    <div data-testid="mui-textfield" {...props}>
      {InputProps?.startAdornment}
      {InputProps?.endAdornment}
    </div>
  )),
  InputAdornment: jest.fn(({ children, ...props }) => <span data-testid={`adornment-${props.position}`}>{children}</span>),
  IconButton: jest.fn(({ children, ...props }) => <button data-testid="icon-btn" {...props}>{children}</button>),
}));
jest.mock('lucide-react', () => ({
  Eye: () => <span data-testid="eye-icon">eye</span>,
  EyeOff: () => <span data-testid="eyeoff-icon">eyeoff</span>,
  Lock: () => <span data-testid="lock-icon">lock</span>,
}));

describe('BasePasswordInput', () => {
  const requiredProps = {
    label: 'Password',
    value: '',
    onChange: jest.fn(),
  };

  it('renders with required props', () => {
    render(<BasePasswordInput {...requiredProps} />);
    expect(screen.getByTestId('mui-textfield')).toBeInTheDocument();
  });

  it('shows lock icon when showStartAdornment is true', () => {
    render(<BasePasswordInput {...requiredProps} showStartAdornment={true} />);
    expect(screen.getByTestId('adornment-start')).toContainElement(screen.getByTestId('lock-icon'));
  });

  it('does not show lock icon when showStartAdornment is false', () => {
    render(<BasePasswordInput {...requiredProps} showStartAdornment={false} />);
    expect(screen.queryByTestId('adornment-start')).toBeNull();
  });

  it('shows eye icon when value is present and toggles to eyeoff on click', () => {
    render(<BasePasswordInput {...requiredProps} value="secret" />);
    // Should show eye icon initially
    expect(screen.getByTestId('eye-icon')).toBeInTheDocument();
    // Simulate click to toggle
    fireEvent.click(screen.getByTestId('icon-btn'));
    // Should show eyeoff icon after toggle
    expect(screen.getByTestId('eyeoff-icon')).toBeInTheDocument();
  });

  it('does not show endAdornment when value is empty', () => {
    render(<BasePasswordInput {...requiredProps} value="" />);
    expect(screen.queryByTestId('adornment-end')).toBeNull();
  });

  it('disables IconButton when disabled is true', () => {
    render(<BasePasswordInput {...requiredProps} value="secret" disabled={true} />);
    expect(screen.getByTestId('icon-btn')).toBeDisabled();
  });

  it('forwards extra props to TextField', () => {
    render(<BasePasswordInput {...requiredProps} data-custom="foo" />);
    expect(screen.getByTestId('mui-textfield')).toHaveAttribute('data-custom', 'foo');
  });

  it('renders with helperText and error', () => {
    const { TextField } = require('@mui/material');
    render(<BasePasswordInput {...requiredProps} helperText="Error!" error={true} />);
    // Check the last call to the TextField mock for correct props
    const lastCall = TextField.mock.calls[TextField.mock.calls.length - 1][0];
    expect(lastCall.helperText).toBe('Error!');
    expect(lastCall.error).toBe(true);
  });
}); 