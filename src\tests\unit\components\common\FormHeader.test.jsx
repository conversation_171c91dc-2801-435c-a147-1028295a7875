import React from 'react';
import { render, screen } from '@testing-library/react';
import FormHeader from '@/components/common/FormHeader';

jest.mock('@mui/material', () => ({
  Typography: jest.fn(({ children, ...props }) => <div data-testid="mui-typography" {...props}>{children}</div>),
}));

describe('FormHeader', () => {
  it('renders title and subtitle when provided', () => {
    render(<FormHeader title="Title" subtitle="Subtitle" />);
    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('Subtitle')).toBeInTheDocument();
  });

  it('renders only title if subtitle is not provided', () => {
    render(<FormHeader title="Title" />);
    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.queryByText('Subtitle')).toBeNull();
  });

  it('renders only subtitle if title is not provided', () => {
    render(<FormHeader subtitle="Subtitle" />);
    expect(screen.getByText('Subtitle')).toBeInTheDocument();
    expect(screen.queryByText('Title')).toBeNull();
  });

  it('applies titleSx and subtitleSx props', () => {
    render(<FormHeader title="T" subtitle="S" titleSx={{ color: 'red' }} subtitleSx={{ color: 'blue' }} />);
    const { Typography } = require('@mui/material');
    // The first call is for title, the second for subtitle
    expect(Typography.mock.calls[0][0].sx).toMatchObject({ color: 'red' });
    expect(Typography.mock.calls[1][0].sx).toMatchObject({ color: 'blue' });
  });

  it('forwards extra props to Typography for title', () => {
    render(<FormHeader title="T" data-custom="foo" />);
    const { Typography } = require('@mui/material');
    expect(Typography.mock.calls[0][0]['data-custom']).toBe('foo');
  });
}); 