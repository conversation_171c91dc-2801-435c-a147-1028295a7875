import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import PasswordFormSection from '@/components/common/PasswordFormSection';

jest.mock('@mui/material', () => ({
  Box: jest.fn(({ children, ...props }) => <form data-testid="mui-box" {...props}>{children}</form>),
}));
jest.mock('@/components/common/PasswordInput', () => jest.fn(() => <input data-testid="password-input" />));
jest.mock('@/components/common/ConfirmPasswordInput', () => jest.fn(() => <input data-testid="confirm-password-input" />));
jest.mock('@/components/common/SubmitButton', () => jest.fn(({ children, ...props }) => <button data-testid="submit-btn" {...props}>{children}</button>));
jest.mock('@/components/common/AlertMessage', () => jest.fn(({ message }) => message ? <div data-testid="alert-msg">{message}</div> : null));

const PasswordInput = require('@/components/common/PasswordInput');
const ConfirmPasswordInput = require('@/components/common/ConfirmPasswordInput');
const SubmitButton = require('@/components/common/SubmitButton');
const AlertMessage = require('@/components/common/AlertMessage');

describe('PasswordFormSection', () => {
  const defaultProps = {
    onSubmit: jest.fn(),
    newPassword: 'pass',
    confirmPassword: 'pass',
    onPasswordChange: jest.fn(),
    onConfirmPasswordChange: jest.fn(),
    onPasswordBlur: jest.fn(),
    onConfirmPasswordBlur: jest.fn(),
    passwordValidation: {
      hasPasswordError: false,
      passwordErrors: [],
      hasConfirmPasswordError: false,
      confirmPasswordError: '',
    },
    passwordLabel: 'Password',
    confirmPasswordLabel: 'Confirm Password',
    buttonText: 'Submit',
    buttonDisabled: false,
    buttonLoading: false,
    apiError: '',
  };

  beforeEach(() => {
    PasswordInput.mockClear();
    ConfirmPasswordInput.mockClear();
    SubmitButton.mockClear();
    AlertMessage.mockClear();
  });

  it('renders all child components with correct props', () => {
    render(<PasswordFormSection {...defaultProps} />);
    expect(screen.getByTestId('password-input')).toBeInTheDocument();
    expect(screen.getByTestId('confirm-password-input')).toBeInTheDocument();
    expect(screen.getByTestId('submit-btn')).toBeInTheDocument();
  });

  it('calls onSubmit when form is submitted', () => {
    render(<PasswordFormSection {...defaultProps} />);
    fireEvent.submit(screen.getByTestId('mui-box'));
    expect(defaultProps.onSubmit).toHaveBeenCalled();
  });

  it('shows AlertMessage when apiError is present', () => {
    render(<PasswordFormSection {...defaultProps} apiError="Error!" />);
    expect(screen.getByTestId('alert-msg')).toHaveTextContent('Error!');
  });

  it('disables child components when disabled is true', () => {
    render(<PasswordFormSection {...defaultProps} disabled={true} />);
    expect(PasswordInput.mock.calls[0][0].disabled).toBe(true);
    expect(ConfirmPasswordInput.mock.calls[0][0].disabled).toBe(true);
  });

  it('passes validation errors to child components', () => {
    const props = {
      ...defaultProps,
      passwordValidation: {
        hasPasswordError: true,
        passwordErrors: ['Too short'],
        hasConfirmPasswordError: true,
        confirmPasswordError: 'No match',
      },
    };
    render(<PasswordFormSection {...props} />);
    expect(PasswordInput.mock.calls[0][0].error).toBe(true);
    expect(PasswordInput.mock.calls[0][0].helperText).toBe('Too short');
    expect(ConfirmPasswordInput.mock.calls[0][0].error).toBe(true);
    expect(ConfirmPasswordInput.mock.calls[0][0].helperText).toBe('No match');
  });
}); 