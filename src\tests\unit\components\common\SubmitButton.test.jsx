import React from 'react';
import { render, screen } from '@testing-library/react';
import SubmitButton from '@/components/common/SubmitButton';

jest.mock('@mui/material', () => ({
  Button: jest.fn(({ children, ...props }) => <button data-testid="mui-button" {...props}>{children}</button>),
  CircularProgress: jest.fn((props) => <span data-testid="circular-progress" {...props} />),
}));

describe('SubmitButton', () => {
  it('renders children by default', () => {
    render(<SubmitButton>Submit</SubmitButton>);
    expect(screen.getByText('Submit')).toBeInTheDocument();
  });

  it('renders CircularProgress when loading', () => {
    render(<SubmitButton loading={true}>Submit</SubmitButton>);
    expect(screen.getByTestId('circular-progress')).toBeInTheDocument();
  });

  it('disables button when disabled or loading', () => {
    render(<SubmitButton disabled={true}>Submit</SubmitButton>);
    render(<SubmitButton loading={true}>Submit</SubmitButton>);
    const buttons = screen.getAllByTestId('mui-button');
    expect(buttons[0]).toBeDisabled();
    expect(buttons[1]).toBeDisabled();
  });

  it('applies variant, size, type, and fullWidth props', () => {
    render(<SubmitButton variant="outlined" size="small" type="button" fullWidth={false}>X</SubmitButton>);
    const { Button } = require('@mui/material');
    const lastCall = Button.mock.calls[Button.mock.calls.length - 1][0];
    expect(lastCall.variant).toBe('outlined');
    expect(lastCall.size).toBe('small');
    expect(lastCall.type).toBe('button');
    expect(lastCall.fullWidth).toBe(false);
  });

  it('merges custom sx and applies authStyle gradient', () => {
    render(<SubmitButton sx={{ authStyle: true, color: 'red' }}>X</SubmitButton>);
    const { Button } = require('@mui/material');
    const lastCall = Button.mock.calls[Button.mock.calls.length - 1][0];
    expect(lastCall.sx.background).toContain('linear-gradient(135deg');
    expect(lastCall.sx.color).toBe('red');
    expect(lastCall.sx.authStyle).toBeUndefined();
  });

  it('forwards extra props to Button', () => {
    render(<SubmitButton data-custom="foo">X</SubmitButton>);
    expect(screen.getByTestId('mui-button')).toHaveAttribute('data-custom', 'foo');
  });
}); 