import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import TermsCheckbox from '@/components/common/TermsCheckbox';

jest.mock('@mui/material', () => ({
  FormControlLabel: jest.fn(({ control, label, ...props }) => <label data-testid="mui-fcl" {...props}>{control}{label}</label>),
  Checkbox: jest.fn((props) => <input data-testid="mui-checkbox" type="checkbox" {...props} />),
  Typography: jest.fn(({ children, ...props }) => <span data-testid="mui-typography" {...props}>{children}</span>),
  Link: jest.fn(({ children, ...props }) => <a data-testid="mui-link" {...props}>{children}</a>),
}));

describe('TermsCheckbox', () => {
  const requiredProps = {
    checked: false,
    onChange: jest.fn(),
  };

  it('renders checkbox and label', () => {
    render(<TermsCheckbox {...requiredProps} />);
    expect(screen.getByTestId('mui-checkbox')).toBeInTheDocument();
    expect(screen.getByText('Terms of Service')).toBeInTheDocument();
    expect(screen.getByText('Privacy Policy')).toBeInTheDocument();
  });

  it('calls onChange when checkbox is clicked', () => {
    render(<TermsCheckbox {...requiredProps} />);
    fireEvent.click(screen.getByTestId('mui-checkbox'));
    expect(requiredProps.onChange).toHaveBeenCalled();
  });

  it('disables checkbox when disabled is true', () => {
    render(<TermsCheckbox {...requiredProps} disabled={true} />);
    expect(screen.getByTestId('mui-checkbox')).toBeDisabled();
  });

  it('shows error color when error is true', () => {
    render(<TermsCheckbox {...requiredProps} error={true} />);
    const { Checkbox, Typography } = require('@mui/material');
    expect(Checkbox.mock.calls[0][0].color).toBe('error');
    expect(Typography.mock.calls[0][0].sx.color).toBe('#f44336');
  });

  it('renders custom terms and privacy URLs', () => {
    render(<TermsCheckbox {...requiredProps} termsUrl="/terms" privacyUrl="/privacy" />);
    const links = screen.getAllByTestId('mui-link');
    expect(links[0]).toHaveAttribute('href', '/terms');
    expect(links[1]).toHaveAttribute('href', '/privacy');
  });

  it('forwards extra props to FormControlLabel', () => {
    const { FormControlLabel } = require('@mui/material');
    render(<TermsCheckbox {...requiredProps} data-custom="foo" />);
    const lastCall = FormControlLabel.mock.calls[FormControlLabel.mock.calls.length - 1][0];
    expect(lastCall['data-custom']).toBe('foo');
  });
}); 