import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import axios from 'axios';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';


// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the env config
jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080'
}));

// Mock import.meta.env
const mockImportMeta = {
  env: {
    VITE_APP_API_URL: 'http://localhost:8080'
  }
};

// Replace import.meta globally
Object.defineProperty(globalThis, 'import', {
  value: {
    meta: mockImportMeta
  },
  writable: true
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Test component that uses useAuth
const TestComponent = () => {
  const { user, isLoading, login, signUp, logout, forgotPassword, resetPassword, setAuthenticatedUser, loginWithGoogle } = useAuth();

  const handleLogin = async () => {
    try {
      await login('<EMAIL>', 'password');
    } catch (error) {
      // Silently catch login errors for testing
    }
  };

  const handleGoogleLogin = async () => {
    try {
      await loginWithGoogle();
    } catch (error) {}
  };

  const handleSetUserWithPic = () => {
    setAuthenticatedUser({ id: '2', name: 'Jane', email: '<EMAIL>', profilePicture: 'https://example.com/pic.jpg' });
  };

  return (
    <div>
      <div data-testid="user">{user ? (user.name || JSON.stringify(user)) : 'null'}</div>
      <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
      <button onClick={handleLogin} data-testid="login-btn">
        Login
      </button>
      <button onClick={() => signUp('John Doe', '<EMAIL>', 'password', '1234567890')} data-testid="signup-btn">
        Sign Up
      </button>
      <button onClick={logout} data-testid="logout-btn">
        Logout
      </button>
      <button onClick={() => forgotPassword('<EMAIL>')} data-testid="forgot-password-btn">
        Forgot Password
      </button>
      <button onClick={() => resetPassword('token', 'newpassword')} data-testid="reset-password-btn">
        Reset Password
      </button>
      <button onClick={() => setAuthenticatedUser({ id: '1', name: 'John', email: '<EMAIL>' })} data-testid="set-user-btn">
        Set User
      </button>
      <button onClick={handleGoogleLogin} data-testid="google-login-btn">
        Google Login
      </button>
      <button onClick={handleSetUserWithPic} data-testid="set-user-pic-btn">
        Set User With Pic
      </button>
    </div>
  );
};

describe('AuthContext Real Implementation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);

    // Use fake timers to handle setTimeout in async functions
    jest.useFakeTimers();

    // Mock IP address API
    mockedAxios.get.mockResolvedValue({
      data: { ip: '***********' }
    });
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('provides initial auth state', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('user')).toHaveTextContent('null');
    expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
  });

  it('handles successful login', async () => {
    mockedAxios.post.mockResolvedValue({
      status: 200,
      data: {
        userId: '1',
        sessionToken: 'token123',
        refreshToken: 'refresh123',
        expiresAt: '2024-12-31T23:59:59Z',
        refreshExpiresAt: '2025-01-31T23:59:59Z'
      }
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const loginBtn = screen.getByTestId('login-btn');

    await act(async () => {
      loginBtn.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('test'); // email prefix
    });

    expect(mockedAxios.post).toHaveBeenCalledWith(
      'http://localhost:8080/api/v1/login',
      expect.objectContaining({
        email: '<EMAIL>',
        password: 'password'
      }),
      expect.any(Object)
    );
  });

  it('handles login failure', async () => {
    mockedAxios.post.mockRejectedValue(new Error('Login failed'));

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const loginBtn = screen.getByTestId('login-btn');

    // The login function throws an error, but the button click doesn't propagate it
    await act(async () => {
      loginBtn.click();
    });

    // Wait for loading to finish
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    expect(screen.getByTestId('user')).toHaveTextContent('null');
  });

  it('handles successful sign up', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const signUpBtn = screen.getByTestId('signup-btn');

    await act(async () => {
      signUpBtn.click();
      // Fast-forward the setTimeout in signUp function (2000ms)
      jest.advanceTimersByTime(2000);
    });

    // SignUp doesn't set user, just simulates API call
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    expect(screen.getByTestId('user')).toHaveTextContent('null');
  });

  it('handles logout', async () => {
    // First set a user
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const setUserBtn = screen.getByTestId('set-user-btn');
    await act(async () => {
      setUserBtn.click();
    });

    expect(screen.getByTestId('user')).toHaveTextContent('John');

    // Then logout
    const logoutBtn = screen.getByTestId('logout-btn');
    await act(async () => {
      logoutBtn.click();
    });

    expect(screen.getByTestId('user')).toHaveTextContent('null');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('userId');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('userName');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('userEmail');
  });

  it('handles forgot password', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const forgotPasswordBtn = screen.getByTestId('forgot-password-btn');

    await act(async () => {
      forgotPasswordBtn.click();
      // Fast-forward the setTimeout in forgotPassword function (1500ms)
      jest.advanceTimersByTime(1500);
    });

    // ForgotPassword just simulates API call, doesn't make actual HTTP request
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });
  });

  it('handles reset password', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const resetPasswordBtn = screen.getByTestId('reset-password-btn');

    await act(async () => {
      resetPasswordBtn.click();
      // Fast-forward the setTimeout in resetPassword function (1500ms)
      jest.advanceTimersByTime(1500);
    });

    // ResetPassword just simulates API call, doesn't make actual HTTP request
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });
  });

  it('sets authenticated user', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const setUserBtn = screen.getByTestId('set-user-btn');
    
    await act(async () => {
      setUserBtn.click();
    });

    expect(screen.getByTestId('user')).toHaveTextContent('John');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('userId', '1');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('userName', 'John');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('userEmail', '<EMAIL>');
  });

  it('throws error when useAuth is used outside AuthProvider', () => {
    const TestComponentOutsideProvider = () => {
      useAuth();
      return <div>Test</div>;
    };

    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<TestComponentOutsideProvider />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });

  it('loads existing user from localStorage on mount', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      switch (key) {
        case 'userId': return '1';
        case 'sessionToken': return 'token123'; // Required for loading user
        case 'userName': return 'John Doe';
        case 'userEmail': return '<EMAIL>';
        default: return null;
      }
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('user')).toHaveTextContent('John Doe');
  });

  it('handles Google login', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const googleBtn = screen.getByTestId('google-login-btn');
    await act(async () => {
      googleBtn.click();
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('Google User');
    });
  });

  it('sets authenticated user with profilePicture', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const setUserPicBtn = screen.getByTestId('set-user-pic-btn');
    await act(async () => {
      setUserPicBtn.click();
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith('userProfilePicture', 'https://example.com/pic.jpg');
  });

  it('loads user with profilePicture from localStorage on mount', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      switch (key) {
        case 'userId': return '3';
        case 'sessionToken': return 'token456';
        case 'userName': return 'Pic User';
        case 'userEmail': return '<EMAIL>';
        case 'userProfilePicture': return 'https://example.com/profile.jpg';
        default: return null;
      }
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('user')).toHaveTextContent('Pic User');
  });
});
