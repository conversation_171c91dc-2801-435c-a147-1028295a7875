import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock environment variables
process.env.VITE_APP_API_URL = 'http://localhost:8080';

// Mock AuthContext functionality for testing

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await mockedAxios.post('http://localhost:8080/api/v1/login', {
        email,
        password,
      });

      if (response.status === 200 && response.data.success) {
        const userData = response.data.user;
        setUser(userData);
        localStorage.setItem('userEmail', email);
        localStorage.setItem('userName', userData.name);
      } else {
        console.error('Login failed:', response.data?.error || 'Login failed');
      }
    } catch (error) {
      console.error('Invalid credentials or connection error');
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (name: string, email: string, password: string) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      throw new Error('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('userId');
    localStorage.removeItem('sessionToken');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userName');
  };

  const forgotPassword = async (email: string) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      throw new Error('Failed to send reset email');
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (token: string, newPassword: string) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      throw new Error('Failed to reset password');
    } finally {
      setIsLoading(false);
    }
  };

  const setAuthenticatedUser = (userData: any) => {
    setUser(userData);
  };

  const loginWithGoogle = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      throw new Error('Google login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue = {
    user,
    isLoading,
    login,
    signUp,
    loginWithGoogle,
    logout,
    forgotPassword,
    resetPassword,
    setAuthenticatedUser,
  };

  return (
    <div data-testid="auth-provider">
      <div data-testid="user">{user ? user.name : 'No user'}</div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
      <button data-testid="login" onClick={() => login('<EMAIL>', 'password')}>
        Login
      </button>
      <button data-testid="signup" onClick={() => signUp('Test User', '<EMAIL>', 'password')}>
        Sign Up
      </button>
      <button data-testid="logout" onClick={() => logout()}>
        Logout
      </button>
      <button data-testid="forgot-password" onClick={() => forgotPassword('<EMAIL>')}>
        Forgot Password
      </button>
      <button data-testid="reset-password" onClick={() => resetPassword('token', 'newpassword')}>
        Reset Password
      </button>
      <button data-testid="set-user" onClick={() => setAuthenticatedUser({ id: '1', name: 'Test User', email: '<EMAIL>' })}>
        Set User
      </button>
      {children}
    </div>
  );
};

// Mock localStorage outside describe block
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

describe('AuthContext Functionality', () => {

  beforeEach(() => {
    jest.clearAllMocks();
    // Replace localStorage with our mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });
    localStorageMock.clear.mockClear();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  it('provides initial auth state', () => {
    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    expect(screen.getByTestId('user')).toHaveTextContent('No user');
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
  });

  it('handles successful login', async () => {
    const mockResponse = {
      status: 200,
      data: {
        success: true,
        user: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>'
        }
      }
    };
    mockedAxios.post.mockResolvedValue(mockResponse);

    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    const loginButton = screen.getByTestId('login');
    
    await act(async () => {
      loginButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('Test User');
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith('userEmail', '<EMAIL>');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('userName', 'Test User');
  });

  it('handles login failure', async () => {
    mockedAxios.post.mockRejectedValue(new Error('Login failed'));

    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    const loginButton = screen.getByTestId('login');

    // Since the error is caught internally, we just verify the user state doesn't change
    await act(async () => {
      try {
        loginButton.click();
        // Wait a bit for the async operation to complete
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        // Error is expected and handled internally
      }
    });

    // Verify that user is still null after failed login
    expect(screen.getByTestId('user')).toHaveTextContent('No user');
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
  });

  it('handles sign up', async () => {
    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    const signupButton = screen.getByTestId('signup');
    
    await act(async () => {
      signupButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    // Sign up should not set user
    expect(screen.getByTestId('user')).toHaveTextContent('No user');
  });

  it('handles logout', async () => {
    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    // First set a user
    const setUserButton = screen.getByTestId('set-user');
    act(() => {
      setUserButton.click();
    });

    expect(screen.getByTestId('user')).toHaveTextContent('Test User');

    // Now logout
    const logoutButton = screen.getByTestId('logout');
    act(() => {
      logoutButton.click();
    });

    expect(screen.getByTestId('user')).toHaveTextContent('No user');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('userId');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('sessionToken');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('userEmail');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('userName');
  });

  it('handles forgot password', async () => {
    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    const forgotPasswordButton = screen.getByTestId('forgot-password');
    
    await act(async () => {
      forgotPasswordButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });
  });

  it('handles reset password', async () => {
    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    const resetPasswordButton = screen.getByTestId('reset-password');
    
    await act(async () => {
      resetPasswordButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });
  });

  it('sets authenticated user', () => {
    render(<MockAuthProvider><div>Test</div></MockAuthProvider>);

    const setUserButton = screen.getByTestId('set-user');
    act(() => {
      setUserButton.click();
    });

    expect(screen.getByTestId('user')).toHaveTextContent('Test User');
  });
});
