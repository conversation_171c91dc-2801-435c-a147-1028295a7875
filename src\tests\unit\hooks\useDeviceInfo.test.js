import { act, renderHook } from '@testing-library/react';
import useDeviceInfo from '@/hooks/useDeviceInfo';

jest.mock('axios');
const axios = require('axios');

describe('useDeviceInfo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches IP address from API and caches it', async () => {
    axios.get.mockResolvedValue({ data: { ip: '*******' } });
    const { result } = renderHook(() => useDeviceInfo());
    let ip;
    await act(async () => {
      ip = await result.current.fetchIpAddress();
    });
    expect(ip).toBe('*******');
    expect(result.current.ipAddress).toBe('*******');
    // Should return cached value
    await act(async () => {
      const cached = await result.current.fetchIpAddress();
      expect(cached).toBe('*******');
    });
  });

  it('returns empty string on fetch error', async () => {
    axios.get.mockRejectedValue(new Error('fail'));
    const { result } = renderHook(() => useDeviceInfo());
    let ip;
    await act(async () => {
      ip = await result.current.fetchIpAddress();
    });
    expect(ip).toBe('');
    expect(result.current.ipAddress).toBe('');
  });

  it('returns device details from navigator.userAgent', () => {
    const userAgent = 'test-agent';
    const originalUA = global.navigator.userAgent;
    Object.defineProperty(global.navigator, 'userAgent', { value: userAgent, configurable: true });
    const { result } = renderHook(() => useDeviceInfo());
    expect(result.current.getDeviceDetails()).toBe(userAgent);
    Object.defineProperty(global.navigator, 'userAgent', { value: originalUA, configurable: true });
  });

  it('getDeviceInfo returns both IP and device details', async () => {
    axios.get.mockResolvedValue({ data: { ip: '*******' } });
    const userAgent = 'ua-2';
    const originalUA = global.navigator.userAgent;
    Object.defineProperty(global.navigator, 'userAgent', { value: userAgent, configurable: true });
    const { result } = renderHook(() => useDeviceInfo());
    let info;
    await act(async () => {
      info = await result.current.getDeviceInfo();
    });
    expect(info).toEqual({ ipAddress: '*******', deviceDetails: userAgent });
    Object.defineProperty(global.navigator, 'userAgent', { value: originalUA, configurable: true });
  });
}); 