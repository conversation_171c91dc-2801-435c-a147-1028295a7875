import { renderHook, act } from '@testing-library/react';
import useFormValidation from '../../../hooks/useFormValidation';

// Mock the validation messages
jest.mock('../../../constants/validationMessages', () => ({
  EMAIL_MESSAGES: {
    REQUIRED: 'Email is required',
    INVALID: 'Please enter a valid email address'
  },
  VALIDATION_MESSAGES: {
    NAME_REQUIRED: 'Name is required',
    NAME_MIN_LENGTH: 'Name must be at least 2 characters',
    MOBILE_REQUIRED: 'Mobile number is required',
    MOBILE_INVALID: 'Please enter a valid mobile number',
    TERMS_REQUIRED: 'You must agree to the terms and conditions'
  },
  REGEX_PATTERNS: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MOBILE: /^[0-9]{10}$/
  }
}));

describe('useFormValidation Enhanced Tests', () => {
  const initialValues = {
    email: '',
    name: '',
    mobile: '',
    agreeToTerms: false
  };

  describe('Initialization', () => {
    it('initializes with default empty values when no initial values provided', () => {
      const { result } = renderHook(() => useFormValidation());
      
      expect(result.current.values).toEqual({});
      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
      expect(result.current.isValid).toBe(true);
    });

    it('initializes with provided initial values', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      expect(result.current.values).toEqual(initialValues);
      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
      expect(result.current.isValid).toBe(true);
    });

    it('initializes with partial initial values', () => {
      const partialValues = { email: '<EMAIL>' };
      const { result } = renderHook(() => useFormValidation(partialValues));
      
      expect(result.current.values).toEqual(partialValues);
      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
      expect(result.current.isValid).toBe(true);
    });
  });

  describe('handleInputChange', () => {
    it('updates field value correctly', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });
      
      expect(result.current.values.email).toBe('<EMAIL>');
    });

    it('updates multiple fields independently', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
        result.current.handleInputChange('name', 'John Doe');
      });
      
      expect(result.current.values.email).toBe('<EMAIL>');
      expect(result.current.values.name).toBe('John Doe');
    });

    it('clears existing error when field value changes', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      // First, create an error
      act(() => {
        result.current.handleBlur('email');
      });
      
      expect(result.current.errors.email).toBe('Email is required');
      
      // Then clear it by changing the value
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });
      
      expect(result.current.errors.email).toBeUndefined();
    });

    it('does not affect other field errors when clearing one field error', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      // Create errors for multiple fields
      act(() => {
        result.current.handleBlur('email');
        result.current.handleBlur('name');
      });
      
      expect(result.current.errors.email).toBe('Email is required');
      expect(result.current.errors.name).toBe('Name is required');
      
      // Clear only email error
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });
      
      expect(result.current.errors.email).toBeUndefined();
      expect(result.current.errors.name).toBe('Name is required');
    });
  });

  describe('handleBlur', () => {
    it('marks field as touched', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      act(() => {
        result.current.handleBlur('email');
      });
      
      expect(result.current.touched.email).toBe(true);
    });

    it('validates field on blur and sets error for invalid value', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      act(() => {
        result.current.handleBlur('email');
      });
      
      expect(result.current.errors.email).toBe('Email is required');
    });

    it('validates field on blur and does not set error for valid value', () => {
      const { result } = renderHook(() => useFormValidation({ email: '<EMAIL>' }));

      act(() => {
        result.current.handleBlur('email');
      });

      expect(result.current.errors.email).toBeUndefined();
      expect(result.current.touched.email).toBe(true);
    });

    it('validates multiple fields independently on blur', () => {
      const { result } = renderHook(() => useFormValidation({ email: '<EMAIL>', name: '' }));

      act(() => {
        result.current.handleBlur('email');
        result.current.handleBlur('name');
      });

      expect(result.current.errors.email).toBeUndefined();
      expect(result.current.errors.name).toBe('Name is required');
      expect(result.current.touched.email).toBe(true);
      expect(result.current.touched.name).toBe(true);
    });
  });

  describe('validateForm', () => {
    it('validates all fields when no specific fields provided', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      let isValid;
      act(() => {
        isValid = result.current.validateForm();
      });
      
      expect(isValid).toBe(false);
      expect(result.current.errors.email).toBe('Email is required');
      expect(result.current.errors.name).toBe('Name is required');
      expect(result.current.errors.mobile).toBe('Mobile number is required');
      expect(result.current.errors.agreeToTerms).toBe('You must agree to the terms and conditions');
    });

    it('validates only specified fields', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      let isValid;
      act(() => {
        isValid = result.current.validateForm(['email', 'name']);
      });
      
      expect(isValid).toBe(false);
      expect(result.current.errors.email).toBe('Email is required');
      expect(result.current.errors.name).toBe('Name is required');
      expect(result.current.errors.mobile).toBeUndefined();
      expect(result.current.errors.agreeToTerms).toBeUndefined();
    });

    it('returns true when all specified fields are valid', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
        result.current.handleInputChange('name', 'John Doe');
      });
      
      let isValid;
      act(() => {
        isValid = result.current.validateForm(['email', 'name']);
      });
      
      expect(isValid).toBe(true);
      expect(result.current.errors.email).toBeUndefined();
      expect(result.current.errors.name).toBeUndefined();
    });

    it('returns false when any specified field is invalid', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));
      
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
        // name is still empty
      });
      
      let isValid;
      act(() => {
        isValid = result.current.validateForm(['email', 'name']);
      });
      
      expect(isValid).toBe(false);
      expect(result.current.errors.email).toBeUndefined();
      expect(result.current.errors.name).toBe('Name is required');
    });
  });

  describe('Field Validation Rules', () => {
    describe('Email validation', () => {
      it('validates required email', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));
        
        act(() => {
          result.current.handleBlur('email');
        });
        
        expect(result.current.errors.email).toBe('Email is required');
      });

      it('validates email format', () => {
        const { result } = renderHook(() => useFormValidation({ email: 'invalid-email' }));

        act(() => {
          result.current.handleBlur('email');
        });

        expect(result.current.errors.email).toBe('Please enter a valid email address');
      });

      it('accepts valid email', () => {
        const { result } = renderHook(() => useFormValidation({ email: '<EMAIL>' }));

        act(() => {
          result.current.handleBlur('email');
        });

        expect(result.current.errors.email).toBeUndefined();
      });
    });

    describe('Name validation', () => {
      it('validates required name', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));
        
        act(() => {
          result.current.handleBlur('name');
        });
        
        expect(result.current.errors.name).toBe('Name is required');
      });

      it('validates name with only whitespace', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));
        
        act(() => {
          result.current.handleInputChange('name', '   ');
          result.current.handleBlur('name');
        });
        
        expect(result.current.errors.name).toBe('Name is required');
      });

      it('validates minimum name length', () => {
        const { result } = renderHook(() => useFormValidation({ name: 'A' }));

        act(() => {
          result.current.handleBlur('name');
        });

        expect(result.current.errors.name).toBe('Name must be at least 2 characters');
      });

      it('accepts valid name', () => {
        const { result } = renderHook(() => useFormValidation({ name: 'John Doe' }));

        act(() => {
          result.current.handleBlur('name');
        });

        expect(result.current.errors.name).toBeUndefined();
      });
    });

    describe('Mobile validation', () => {
      it('validates required mobile', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));
        
        act(() => {
          result.current.handleBlur('mobile');
        });
        
        expect(result.current.errors.mobile).toBe('Mobile number is required');
      });

      it('validates mobile format', () => {
        const { result } = renderHook(() => useFormValidation({ mobile: '123' }));

        act(() => {
          result.current.handleBlur('mobile');
        });

        expect(result.current.errors.mobile).toBe('Please enter a valid mobile number');
      });

      it('accepts valid mobile', () => {
        const { result } = renderHook(() => useFormValidation({ mobile: '1234567890' }));

        act(() => {
          result.current.handleBlur('mobile');
        });

        expect(result.current.errors.mobile).toBeUndefined();
      });
    });

    describe('Terms validation', () => {
      it('validates required terms agreement', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));
        
        act(() => {
          result.current.handleBlur('agreeToTerms');
        });
        
        expect(result.current.errors.agreeToTerms).toBe('You must agree to the terms and conditions');
      });

      it('accepts terms agreement', () => {
        const { result } = renderHook(() => useFormValidation({ agreeToTerms: true }));

        act(() => {
          result.current.handleBlur('agreeToTerms');
        });

        expect(result.current.errors.agreeToTerms).toBeUndefined();
      });
    });
  });

  describe('Utility Functions', () => {
    describe('resetForm', () => {
      it('resets all form state to initial values', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));

        // Make some changes
        act(() => {
          result.current.handleInputChange('email', '<EMAIL>');
          result.current.handleInputChange('name', 'John Doe');
          result.current.handleBlur('email');
          result.current.handleBlur('name');
        });

        // Verify changes were made
        expect(result.current.values.email).toBe('<EMAIL>');
        expect(result.current.values.name).toBe('John Doe');
        expect(result.current.touched.email).toBe(true);
        expect(result.current.touched.name).toBe(true);

        // Reset form
        act(() => {
          result.current.resetForm();
        });

        // Verify reset
        expect(result.current.values).toEqual(initialValues);
        expect(result.current.errors).toEqual({});
        expect(result.current.touched).toEqual({});
      });

      it('resets to different initial values when hook is re-initialized', () => {
        const newInitialValues = { email: '<EMAIL>', name: 'New Name' };
        const { result, rerender } = renderHook(
          ({ initialVals }) => useFormValidation(initialVals),
          { initialProps: { initialVals: initialValues } }
        );

        // Make changes
        act(() => {
          result.current.handleInputChange('email', '<EMAIL>');
        });

        // Re-render with new initial values
        rerender({ initialVals: newInitialValues });

        // Reset should use new initial values
        act(() => {
          result.current.resetForm();
        });

        expect(result.current.values).toEqual(newInitialValues);
      });
    });

    describe('setFieldValue', () => {
      it('sets field value directly', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));

        act(() => {
          result.current.setFieldValue('email', '<EMAIL>');
        });

        expect(result.current.values.email).toBe('<EMAIL>');
      });

      it('does not clear existing errors when setting value directly', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));

        // Create an error
        act(() => {
          result.current.handleBlur('email');
        });

        expect(result.current.errors.email).toBe('Email is required');

        // Set value directly (should not clear error)
        act(() => {
          result.current.setFieldValue('email', '<EMAIL>');
        });

        expect(result.current.values.email).toBe('<EMAIL>');
        expect(result.current.errors.email).toBe('Email is required');
      });
    });

    describe('setFieldError', () => {
      it('sets field error directly', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));

        act(() => {
          result.current.setFieldError('email', 'Custom error message');
        });

        expect(result.current.errors.email).toBe('Custom error message');
      });

      it('overwrites existing error', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));

        // Create initial error
        act(() => {
          result.current.handleBlur('email');
        });

        expect(result.current.errors.email).toBe('Email is required');

        // Overwrite with custom error
        act(() => {
          result.current.setFieldError('email', 'Server validation error');
        });

        expect(result.current.errors.email).toBe('Server validation error');
      });
    });

    describe('setFieldTouched', () => {
      it('sets field touched state to true by default', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));

        act(() => {
          result.current.setFieldTouched('email');
        });

        expect(result.current.touched.email).toBe(true);
      });

      it('sets field touched state to specified value', () => {
        const { result } = renderHook(() => useFormValidation(initialValues));

        // Set to true
        act(() => {
          result.current.setFieldTouched('email', true);
        });

        expect(result.current.touched.email).toBe(true);

        // Set to false
        act(() => {
          result.current.setFieldTouched('email', false);
        });

        expect(result.current.touched.email).toBe(false);
      });
    });
  });

  describe('isValid computed property', () => {
    it('returns true when no errors exist', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));

      expect(result.current.isValid).toBe(true);
    });

    it('returns false when errors exist', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));

      act(() => {
        result.current.handleBlur('email');
      });

      expect(result.current.isValid).toBe(false);
    });

    it('updates when errors are cleared', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));

      // Create error
      act(() => {
        result.current.handleBlur('email');
      });

      expect(result.current.isValid).toBe(false);

      // Clear error
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });

      expect(result.current.isValid).toBe(true);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles unknown field validation gracefully', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));

      act(() => {
        result.current.handleInputChange('unknownField', 'value');
        result.current.handleBlur('unknownField');
      });

      expect(result.current.values.unknownField).toBe('value');
      expect(result.current.touched.unknownField).toBe(true);
      expect(result.current.errors.unknownField).toBeUndefined();
    });

    it('handles null and undefined values', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));

      act(() => {
        result.current.handleInputChange('email', null);
        result.current.handleInputChange('name', undefined);
      });

      expect(result.current.values.email).toBe(null);
      expect(result.current.values.name).toBe(undefined);
    });

    it('handles empty string validation for name field', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));

      act(() => {
        result.current.handleInputChange('name', '');
        result.current.handleBlur('name');
      });

      expect(result.current.errors.name).toBe('Name is required');
    });

    it('handles validation with mixed valid and invalid fields', () => {
      const { result } = renderHook(() => useFormValidation(initialValues));

      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
        result.current.handleInputChange('name', ''); // invalid
        result.current.handleInputChange('mobile', '1234567890');
        result.current.handleInputChange('agreeToTerms', false); // invalid
      });

      let isValid;
      act(() => {
        isValid = result.current.validateForm();
      });

      expect(isValid).toBe(false);
      expect(result.current.errors.email).toBeUndefined();
      expect(result.current.errors.name).toBe('Name is required');
      expect(result.current.errors.mobile).toBeUndefined();
      expect(result.current.errors.agreeToTerms).toBe('You must agree to the terms and conditions');
    });
  });

  describe('Performance and Memory', () => {
    it('maintains function reference stability', () => {
      const { result, rerender } = renderHook(() => useFormValidation(initialValues));

      const initialFunctions = {
        validateForm: result.current.validateForm,
        handleInputChange: result.current.handleInputChange,
        handleBlur: result.current.handleBlur,
        resetForm: result.current.resetForm,
        setFieldValue: result.current.setFieldValue,
        setFieldError: result.current.setFieldError,
        setFieldTouched: result.current.setFieldTouched
      };

      // Re-render and check if functions are the same
      rerender();

      expect(result.current.validateForm).toBe(initialFunctions.validateForm);
      expect(result.current.handleInputChange).toBe(initialFunctions.handleInputChange);
      expect(result.current.handleBlur).toBe(initialFunctions.handleBlur);
      expect(result.current.resetForm).toBe(initialFunctions.resetForm);
      expect(result.current.setFieldValue).toBe(initialFunctions.setFieldValue);
      expect(result.current.setFieldError).toBe(initialFunctions.setFieldError);
      expect(result.current.setFieldTouched).toBe(initialFunctions.setFieldTouched);
    });
  });
});
