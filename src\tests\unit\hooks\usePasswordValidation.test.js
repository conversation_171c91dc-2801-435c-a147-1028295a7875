import usePasswordValidation from '@/hooks/usePasswordValidation';
import { renderHook } from '@testing-library/react';

jest.mock('@/constants/validationMessages', () => ({
  AUTH_RULES: [
    { regex: /^.{8,}$/, message: 'At least 8 chars' },
    { regex: /[A-Z]/, message: 'Uppercase' },
    { regex: /[a-z]/, message: 'Lowercase' },
    { regex: /[0-9]/, message: 'Number' },
  ],
  AUTH_MESSAGES: {
    REQUIRED: 'Required',
    CONFIRM_REQUIRED: 'Confirm required',
    MISMATCH: 'Mismatch',
  },
}));

describe('usePasswordValidation', () => {
  it('returns required error if password is empty', () => {
    const { result } = renderHook(() => usePasswordValidation('', '', { password: true }));
    expect(result.current.passwordErrors).toContain('Required');
    expect(result.current.hasPasswordError).toBe(true);
  });

  it('returns rule errors for invalid password', () => {
    const { result } = renderHook(() => usePasswordValidation('abc', '', { password: true }));
    expect(result.current.passwordErrors).toContain('At least 8 chars');
    expect(result.current.passwordErrors).toContain('Uppercase');
    expect(result.current.passwordErrors).toContain('Number');
    expect(result.current.hasPasswordError).toBe(true);
  });

  it('returns no errors for valid password', () => {
    const { result } = renderHook(() => usePasswordValidation('Abcdefg1', '', { password: true }));
    expect(result.current.passwordErrors.length).toBe(0);
    expect(result.current.isPasswordValid).toBe(true);
  });

  it('returns confirm required if confirmPassword is empty and touched', () => {
    const { result } = renderHook(() => usePasswordValidation('Abcdefg1', '', { confirmPassword: true }));
    expect(result.current.confirmPasswordError).toBe('Confirm required');
    expect(result.current.hasConfirmPasswordError).toBe(true);
  });

  it('returns mismatch if passwords do not match', () => {
    const { result } = renderHook(() => usePasswordValidation('Abcdefg1', 'Abcdefg2', { confirmPassword: true }));
    expect(result.current.confirmPasswordError).toBe('Mismatch');
    expect(result.current.hasConfirmPasswordError).toBe(true);
    expect(result.current.passwordsMatch).toBe(false);
  });

  it('returns passwordsMatch true if passwords match', () => {
    const { result } = renderHook(() => usePasswordValidation('Abcdefg1', 'Abcdefg1', { confirmPassword: true }));
    expect(result.current.passwordsMatch).toBe(true);
    expect(result.current.isValid).toBe(true);
  });

  it('isValid is false if password is invalid or passwords do not match', () => {
    const { result } = renderHook(() => usePasswordValidation('abc', 'abc', { password: true, confirmPassword: true }));
    expect(result.current.isValid).toBe(false);
  });
}); 