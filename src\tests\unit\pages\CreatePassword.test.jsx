import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import CreatePassword from '@/pages/CreatePassword';
import axios from 'axios';
import { TEST_DATA } from '@/constants/validationMessages';
import { SECURE_TEST_DATA } from '@/config/testConfig';

// Mock GoogleLoginButton to avoid import.meta.env issues
jest.mock('@/components/GoogleLoginButton', () => ({
  __esModule: true,
  default: () => <div data-testid="google-login-button" />
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
let mockSearchParams = new URLSearchParams();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useSearchParams: () => [mockSearchParams],
}));

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock AuthContext
const mockSetAuthenticatedUser = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    setAuthenticatedUser: mockSetAuthenticatedUser,
  }),
}));

// Mock environment configuration
jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080',
}));

// Mock MUI components
jest.mock('@mui/material', () => {
  const React = require('react');
  return {
    Box: ({ children, component, ...props }) => {
      if (component === 'form') {
        return <form data-testid="box" {...props}>{children}</form>;
      }
      return <div data-testid="box" {...props}>{children}</div>;
    },
    Paper: ({ children, elevation, sx, ...props }) => (
      <div data-testid="paper" data-elevation={elevation} {...props}>{children}</div>
    ),
    Typography: ({ children, variant, component, gutterBottom, sx, ...props }) => (
      <div data-testid="typography" data-variant={variant} data-component={component} {...props}>
        {children}
      </div>
    ),
    TextField: ({ label, value, onChange, fullWidth, margin, variant, type, error, helperText, InputProps, ...props }) => {
      const inputId = `input-${label.replace(/\s+/g, '-').toLowerCase()}`;
      const [internalValue, setInternalValue] = React.useState(value || '');
      React.useEffect(() => { setInternalValue(value || ''); }, [value]);
      const handleChange = (e) => {
        setInternalValue(e.target.value);
        if (onChange) onChange(e);
      };
      return (
        <div data-testid="text-field-container">
          <label htmlFor={inputId}>{label}</label>
          <input
            id={inputId}
            data-testid="text-field"
            data-label={label}
            value={internalValue}
            onChange={handleChange}
            data-fullwidth={fullWidth}
            data-margin={margin}
            data-variant={variant}
            type={type}
            data-error={error}
            placeholder={label}
            {...props}
          />
          {helperText && <div data-testid="helper-text">{helperText}</div>}
          {InputProps && InputProps.endAdornment && (
            <div data-testid="input-adornment" data-position="end">
              {InputProps.endAdornment}
            </div>
          )}
          {InputProps && InputProps.startAdornment && (
            <div data-testid="input-adornment" data-position="start">
              {InputProps.startAdornment}
            </div>
          )}
        </div>
      );
    },
    Button: ({ children, variant, color, fullWidth, sx, onClick, disabled, type, ...props }) => {
      // If type is submit, trigger the form's submit event on click
      const handleClick = (e) => {
        if (type === 'submit') {
          // Find the closest form and dispatch a submit event
          let el = e.target;
          while (el && el.nodeName !== 'FORM' && el.parentNode) {
            el = el.parentNode;
          }
          if (el && el.nodeName === 'FORM') {
            const event = new Event('submit', { bubbles: true, cancelable: true });
            el.dispatchEvent(event);
          }
        }
        if (onClick) onClick(e);
      };
      return (
        <button
          data-testid="button"
          data-variant={variant}
          data-fullwidth={fullWidth}
          type={type}
          disabled={disabled}
          onClick={handleClick}
          {...props}
        >
          {children}
        </button>
      );
    },
    InputAdornment: ({ children, position }) => (
      <div data-testid="input-adornment" data-position={position}>{children}</div>
    ),
    IconButton: ({ children, ...props }) => <button data-testid="icon-button" {...props}>{children}</button>,
  };
});

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Eye: () => <div data-testid="eye-icon" />,
  EyeOff: () => <div data-testid="eye-off-icon" />,
  Lock: () => <div data-testid="lock-icon" />,
}));

// Mock AlertMessage to avoid dependency on MUI Alert in tests
jest.mock('@/components/common/AlertMessage', () => ({
  __esModule: true,
  default: ({ children, severity, message }) => (
    <div data-testid="alert" data-severity={severity}>
      {message}
      {children}
    </div>
  ),
}));

// Mock custom components used in CreatePassword.jsx
jest.mock('@/components/common/SubmitButton', () => ({
  __esModule: true,
  default: ({ children, ...props }) => <button {...props}>{children}</button>,
}));
jest.mock('@/components/common/FormContainer', () => ({
  __esModule: true,
  default: ({ children }) => (
    <div data-testid="form-container">
      <div data-testid="paper">{children}</div>
    </div>
  ),
}));
jest.mock('@/components/common/FormHeader', () => ({
  __esModule: true,
  default: ({ children, title, subtitle }) => <div data-testid="form-header">{title && <div>{title}</div>}{subtitle && <div>{subtitle}</div>}{children}</div>,
}));
jest.mock('@/components/common/BasePasswordInput', () => ({
  __esModule: true,
  default: ({ label, ...props }) => {
    const id = `input-${label.replace(/\s+/g, '-').toLowerCase()}`;
    return (
      <div>
        <label htmlFor={id}>{label}</label>
        <input id={id} aria-label={label} placeholder={label} {...props} />
      </div>
    );
  },
}));
jest.mock('@/components/common/PasswordInput', () => ({
  __esModule: true,
  default: ({ label, helperText, type, ...props }) => {
    const id = `input-${label.replace(/\s+/g, '-').toLowerCase()}`;
    return (
      <div>
        <label htmlFor={id}>{label}</label>
        <input id={id} aria-label={label} placeholder={label} type={type || 'password'} {...props} />
        {helperText && <div data-testid="helper-text">{helperText}</div>}
      </div>
    );
  },
}));
jest.mock('@/components/common/ConfirmPasswordInput', () => ({
  __esModule: true,
  default: ({ label, helperText, type, ...props }) => {
    const id = `input-${label.replace(/\s+/g, '-').toLowerCase()}`;
    return (
      <div>
        <label htmlFor={id}>{label}</label>
        <input id={id} aria-label={label} placeholder={label} type={type || 'password'} {...props} />
        {helperText && <div data-testid="helper-text">{helperText}</div>}
      </div>
    );
  },
}));

// ErrorBoundary for testing thrown errors
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { error: null };
  }
  static getDerivedStateFromError(error) {
    return { error };
  }
  render() {
    if (this.state.error) {
      return <div data-testid="error-message">{this.state.error.message}</div>;
    }
    return this.props.children;
  }
}

// ErrorBoundary for testing thrown errors
class TestErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { error: null };
  }
  static getDerivedStateFromError(error) {
    return { error };
  }
  render() {
    if (this.state.error) {
      return <div data-testid="error-message">{this.state.error.message || String(this.state.error)}</div>;
    }
    return this.props.children;
  }
}

describe('CreatePassword Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
    mockSearchParams = new URLSearchParams();
    mockSetAuthenticatedUser.mockClear();
    mockedAxios.get.mockClear();
    mockedAxios.post.mockClear();
  });

  const renderWithRouter = (component) => {
    return render(
      <BrowserRouter>
        {component}
      </BrowserRouter>
    );
  };

  describe('Token Validation', () => {
    test('should show error when no token is provided', async () => {
      // Arrange - No token in search params
      mockSearchParams.delete('token');

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('No token provided.')).toBeInTheDocument();
      });
    });

    test('should show verifying message initially when token is present', () => {
      // Arrange
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockImplementation(() => new Promise(() => {})); // Never resolves

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      expect(screen.getByText('Verifying your email...')).toBeInTheDocument();
    });

    test('should fetch email when valid token is provided', async () => {
      // Arrange
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await screen.findByText('Your account has been activated successfully.');
      expect(screen.getByLabelText('New Password')).toBeInTheDocument();
      expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument();
    });

    test('should handle already activated user', async () => {
      // Arrange
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 409,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('User already activated')).toBeInTheDocument();
        expect(screen.getByLabelText('New Password')).toBeInTheDocument();
        expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument();
      });
    });

    test('should handle invalid token error', async () => {
      // Arrange
      mockSearchParams.set('token', 'invalid-token');
      mockedAxios.get.mockResolvedValue({
        status: 400,
        data: { error: 'Invalid token' }
      });

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Invalid token')).toBeInTheDocument();
      });
    });

    test('should handle network error during token validation', async () => {
      // Arrange
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Invalid or expired token.')).toBeInTheDocument();
      });
    });
  });

  describe('Form Rendering', () => {
    beforeEach(async () => {
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });
    });

    test('should render create password form', async () => {
      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getAllByText('Create Password')[0]).toBeInTheDocument();
        expect(screen.getByLabelText('New Password')).toBeInTheDocument();
        expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument();
      });
    });

    test('should render form elements with correct attributes', async () => {
      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        const passwordField = screen.getByLabelText('New Password');
        const confirmField = screen.getByLabelText('Confirm Password');

        expect(passwordField).toHaveAttribute('type', 'password');
        expect(confirmField).toHaveAttribute('type', 'password');
      });
    });

    test('should render submit button', async () => {
      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'Create Password' })).toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    beforeEach(async () => {
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });
    });

    test('should show password validation errors', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<CreatePassword />);

      await waitFor(() => {
        expect(screen.getByLabelText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByLabelText('New Password');
      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.WEAK);
      await user.tab(); // Trigger blur to show validation

      // Assert
      await waitFor(() => {
        expect(screen.getByText('At least 8 characters')).toBeInTheDocument();
      });
    });

    test('should show password mismatch error', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<CreatePassword />);

      await waitFor(() => {
        expect(screen.getByLabelText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');

      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.DIFFERENT);
      await user.tab(); // Trigger validation

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Passwords do not match')).toBeInTheDocument();
      });
    });

    test('should enable submit button when passwords are valid and match', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<CreatePassword />);

      await waitFor(() => {
        expect(screen.getByLabelText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');

      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.VALID);

      // Assert
      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: 'Create Password' });
        expect(submitButton).not.toBeDisabled();
      });
    });
  });

  describe('Component Structure', () => {
    beforeEach(async () => {
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });
    });

    test('should render with proper structure', async () => {
      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByTestId('paper')).toBeInTheDocument();
        expect(screen.getAllByText('Create Password')[0]).toBeInTheDocument();
        expect(screen.getByText('Please set a strong password for your account')).toBeInTheDocument();
      });
    });

    test('should handle component lifecycle', async () => {
      // Act
      const { unmount } = renderWithRouter(<CreatePassword />);
      
      // Assert - Should render without errors
      await waitFor(() => {
        expect(screen.getAllByText('Create Password')[0]).toBeInTheDocument();
      });
      
      // Act - Unmount component
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('API Integration', () => {
    beforeEach(async () => {
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });
    });

    test('should submit form with valid data', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockResolvedValue({
        status: 200,
        data: {
          userId: 'user123',
          sessionToken: 'session123',
          refreshToken: 'refresh123',
          expiresAt: '2024-12-31T23:59:59Z',
          refreshExpiresAt: '2025-01-31T23:59:59Z',
        },
      });
      renderWithRouter(<CreatePassword />);
      await waitFor(() => expect(screen.getByLabelText('New Password')).toBeInTheDocument());
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Create Password' });
      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.click(submitButton);
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:8080/api/v1/create-user',
          expect.objectContaining({
            email: '<EMAIL>',
            password: SECURE_TEST_DATA.PASSWORDS.VALID,
          }),
          expect.objectContaining({
            headers: { 'Content-Type': 'application/json' },
            validateStatus: expect.any(Function),
          })
        );
      });
    });

    test('should handle API success and authenticate user', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockResolvedValue({
        status: 200,
        data: {
          userId: 'user123',
          sessionToken: 'session123',
          refreshToken: 'refresh123',
          expiresAt: '2024-12-31T23:59:59Z',
          refreshExpiresAt: '2025-01-31T23:59:59Z',
        },
      });
      renderWithRouter(<CreatePassword />);
      await waitFor(() => expect(screen.getByLabelText('New Password')).toBeInTheDocument());
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Create Password' });
      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.click(submitButton);
      await waitFor(() => {
        expect(screen.getByText((text) => text.includes('created successfully'))).toBeInTheDocument();
      });
    });

    test('should handle API error', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockRejectedValue({
        response: { status: 400, data: { message: 'API error' } },
      });
      renderWithRouter(<CreatePassword />);
      await waitFor(() => expect(screen.getByLabelText('New Password')).toBeInTheDocument());
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');
      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.VALID);
      const submitButton = screen.getByRole('button', { name: /create password/i });
      // Catch the error so the test doesn't fail
      await user.click(submitButton).catch(() => {});
      // Wait for error alert to appear
      await waitFor(() => {
        const alerts = screen.getAllByTestId('alert');
        expect(alerts.some(a => a.textContent.includes('Failed to create password'))).toBe(true);
      });
    });

    test('should handle network error', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockRejectedValue(new Error('Network error'));
      renderWithRouter(<CreatePassword />);
      await waitFor(() => expect(screen.getByLabelText('New Password')).toBeInTheDocument());
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');
      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.VALID);
      const submitButton = screen.getByRole('button', { name: /create password/i });
      // Catch the error so the test doesn't fail
      await user.click(submitButton).catch(() => {});
      // Wait for error alert to appear
      await waitFor(() => {
        const alerts = screen.getAllByTestId('alert');
        expect(alerts.some(a => a.textContent.includes('Failed to create password'))).toBe(true);
      });
    });
  });

  describe('Error Handling', () => {
    test('should render without crashing', () => {
      // Act & Assert - Should not throw
      expect(() => renderWithRouter(<CreatePassword />)).not.toThrow();
    });

    test('should handle missing token gracefully', () => {
      // Arrange - No token
      mockSearchParams.delete('token');

      // Act & Assert - Should not throw
      expect(() => renderWithRouter(<CreatePassword />)).not.toThrow();
    });

    test('should handle API errors gracefully', async () => {
      // Arrange
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Invalid or expired token.')).toBeInTheDocument();
      });
    });

    test('should disable form when email verification fails', async () => {
      // Arrange
      mockSearchParams.set('token', 'invalid-token');
      mockedAxios.get.mockResolvedValue({
        status: 400,
        data: { error: 'Invalid token' }
      });

      // Act
      renderWithRouter(<CreatePassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Invalid token')).toBeInTheDocument();
      });
    });
  });

  describe('Side Effects', () => {
    beforeEach(async () => {
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });
      jest.spyOn(window.localStorage.__proto__, 'setItem');
      window.localStorage.__proto__.setItem = jest.fn();
    });

    afterEach(() => {
      window.localStorage.__proto__.setItem.mockRestore && window.localStorage.__proto__.setItem.mockRestore();
    });

    test('should set localStorage tokens on successful password creation', async () => {
      const user = userEvent.setup();
      mockedAxios.post.mockResolvedValue({
        status: 200,
        data: {
          userId: 'user123',
          sessionToken: 'session123',
          refreshToken: 'refresh123',
          expiresAt: '2024-12-31T23:59:59Z',
          refreshExpiresAt: '2025-01-31T23:59:59Z',
        },
      });
      renderWithRouter(<CreatePassword />);
      await waitFor(() => expect(screen.getByLabelText('New Password')).toBeInTheDocument());
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Create Password' });
      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.click(submitButton);
      await waitFor(() => {
        expect(window.localStorage.setItem).toHaveBeenCalledWith('userId', 'user123');
        expect(window.localStorage.setItem).toHaveBeenCalledWith('sessionToken', 'session123');
        expect(window.localStorage.setItem).toHaveBeenCalledWith('refreshToken', 'refresh123');
        expect(window.localStorage.setItem).toHaveBeenCalledWith('expiresAt', '2024-12-31T23:59:59Z');
        expect(window.localStorage.setItem).toHaveBeenCalledWith('refreshExpiresAt', '2025-01-31T23:59:59Z');
      });
    });

    test('should navigate to /dashboard after successful password creation', async () => {
      const user = userEvent.setup();
      mockedAxios.post.mockResolvedValue({
        status: 200,
        data: {
          userId: 'user123',
          sessionToken: 'session123',
          refreshToken: 'refresh123',
          expiresAt: '2024-12-31T23:59:59Z',
          refreshExpiresAt: '2025-01-31T23:59:59Z',
        },
      });
      renderWithRouter(<CreatePassword />);
      await waitFor(() => expect(screen.getByLabelText('New Password')).toBeInTheDocument());
      const passwordField = screen.getByLabelText('New Password');
      const confirmField = screen.getByLabelText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Create Password' });
      await user.type(passwordField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.type(confirmField, SECURE_TEST_DATA.PASSWORDS.VALID);
      await user.click(submitButton);
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
      });
    });
  });

  describe('Button Disabled State', () => {
    beforeEach(async () => {
      mockSearchParams.set('token', 'valid-token-123');
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { email: '<EMAIL>', name: 'Test User', mobile_number: '**********' }
      });
    });

    test('should disable submit button when email is loading', async () => {
      // Simulate loading by not resolving axios.get
      mockedAxios.get.mockImplementation(() => new Promise(() => {}));
      renderWithRouter(<CreatePassword />);
      expect(screen.getByRole('button', { name: 'Create Password' })).toBeDisabled();
    });

    test('should disable submit button when there is an email error', async () => {
      mockedAxios.get.mockResolvedValue({ status: 400, data: { error: 'Invalid token' } });
      renderWithRouter(<CreatePassword />);
      await waitFor(() => {
        expect(screen.getByText('Invalid token')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Create Password' })).toBeDisabled();
      });
    });

    test('should disable submit button when passwords are empty or invalid', async () => {
      renderWithRouter(<CreatePassword />);
      await waitFor(() => expect(screen.getByLabelText('New Password')).toBeInTheDocument());
      const submitButton = screen.getByRole('button', { name: 'Create Password' });
      expect(submitButton).toBeDisabled();
    });
  });
});
