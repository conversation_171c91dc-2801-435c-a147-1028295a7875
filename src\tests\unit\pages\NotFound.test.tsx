import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import NotFound from '@/pages/NotFound';

// Mock window.location for navigation testing
const mockLocation = {
  href: '',
  assign: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn(),
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('NotFound Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocation.href = '';
  });

  describe('Component Rendering', () => {
    test('should render 404 error message', () => {
      // Act
      render(<NotFound />);

      // Assert
      expect(screen.getByText('404')).toBeInTheDocument();
    });

    test('should render page not found message', () => {
      // Act
      render(<NotFound />);

      // Assert
      expect(screen.getByText('Oops! Page not found')).toBeInTheDocument();
    });

    test('should render return to home link', () => {
      // Act
      render(<NotFound />);

      // Assert
      const homeLink = screen.getByText('Return to Home');
      expect(homeLink).toBeInTheDocument();
      expect(homeLink.closest('a')).toHaveAttribute('href', '/');
    });

    test('should render with correct CSS classes', () => {
      // Act
      const { container } = render(<NotFound />);

      // Assert
      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass('min-h-screen', 'flex', 'items-center', 'justify-center', 'bg-gray-100');
      
      const textContainer = mainContainer.firstChild;
      expect(textContainer).toHaveClass('text-center');
    });
  });

  describe('Typography and Styling', () => {
    test('should render 404 heading with correct styling', () => {
      // Act
      render(<NotFound />);

      // Assert
      const heading = screen.getByText('404');
      expect(heading).toHaveClass('text-4xl', 'font-bold', 'mb-4');
      expect(heading.tagName).toBe('H1');
    });

    test('should render description with correct styling', () => {
      // Act
      render(<NotFound />);

      // Assert
      const description = screen.getByText('Oops! Page not found');
      expect(description).toHaveClass('text-xl', 'text-gray-600', 'mb-4');
      expect(description.tagName).toBe('P');
    });

    test('should render home link with correct styling', () => {
      // Act
      render(<NotFound />);

      // Assert
      const homeLink = screen.getByText('Return to Home');
      expect(homeLink).toHaveClass('text-blue-500', 'hover:text-blue-700', 'underline');
    });
  });

  describe('Navigation Functionality', () => {
    test('should have correct href attribute on home link', () => {
      // Act
      render(<NotFound />);

      // Assert
      const homeLink = screen.getByText('Return to Home').closest('a');
      expect(homeLink).toHaveAttribute('href', '/');
    });

    test('should be clickable home link', async () => {
      // Arrange
      const user = userEvent.setup();
      render(<NotFound />);

      // Act
      const homeLink = screen.getByText('Return to Home');
      
      // Assert - Link should be clickable (this tests the element is properly rendered as a link)
      expect(homeLink.closest('a')).toBeInTheDocument();
      
      // Verify it's focusable
      await user.tab();
      expect(homeLink).toHaveFocus();
    });
  });

  describe('Accessibility', () => {
    test('should have proper heading hierarchy', () => {
      // Act
      render(<NotFound />);

      // Assert
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveTextContent('404');
    });

    test('should have accessible link', () => {
      // Act
      render(<NotFound />);

      // Assert
      const link = screen.getByRole('link', { name: 'Return to Home' });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/');
    });

    test('should be keyboard navigable', async () => {
      // Arrange
      const user = userEvent.setup();
      render(<NotFound />);

      // Act
      await user.tab();

      // Assert
      const homeLink = screen.getByText('Return to Home');
      expect(homeLink).toHaveFocus();
    });
  });

  describe('Content Verification', () => {
    test('should display correct error code', () => {
      // Act
      render(<NotFound />);

      // Assert
      expect(screen.getByText('404')).toBeInTheDocument();
    });

    test('should display user-friendly error message', () => {
      // Act
      render(<NotFound />);

      // Assert
      expect(screen.getByText('Oops! Page not found')).toBeInTheDocument();
    });

    test('should provide clear navigation option', () => {
      // Act
      render(<NotFound />);

      // Assert
      expect(screen.getByText('Return to Home')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    test('should render complete component structure', () => {
      // Act
      const { container } = render(<NotFound />);

      // Assert
      expect(container.firstChild).toBeInTheDocument();
      expect(container.firstChild.firstChild).toBeInTheDocument();
      expect(screen.getByText('404')).toBeInTheDocument();
      expect(screen.getByText('Oops! Page not found')).toBeInTheDocument();
      expect(screen.getByText('Return to Home')).toBeInTheDocument();
    });

    test('should have proper nesting structure', () => {
      // Act
      const { container } = render(<NotFound />);

      // Assert
      const outerDiv = container.firstChild;
      const innerDiv = outerDiv.firstChild;
      const heading = innerDiv.firstChild;
      const paragraph = heading.nextSibling;
      const link = paragraph.nextSibling;

      expect(heading.textContent).toBe('404');
      expect(paragraph.textContent).toBe('Oops! Page not found');
      expect(link.textContent).toBe('Return to Home');
    });
  });

  describe('Error Handling', () => {
    test('should render without crashing', () => {
      // Act & Assert
      expect(() => render(<NotFound />)).not.toThrow();
    });

    test('should handle multiple renders', () => {
      // Act
      const { rerender } = render(<NotFound />);
      
      // Assert
      expect(screen.getByText('404')).toBeInTheDocument();
      
      // Act - Rerender
      rerender(<NotFound />);
      
      // Assert
      expect(screen.getByText('404')).toBeInTheDocument();
    });

    test('should handle unmounting gracefully', () => {
      // Act
      const { unmount } = render(<NotFound />);
      
      // Assert - Should not throw on unmount
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Responsive Design', () => {
    test('should use responsive classes for layout', () => {
      // Act
      const { container } = render(<NotFound />);

      // Assert
      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass('min-h-screen');
      expect(mainContainer).toHaveClass('flex');
      expect(mainContainer).toHaveClass('items-center');
      expect(mainContainer).toHaveClass('justify-center');
    });

    test('should center content properly', () => {
      // Act
      const { container } = render(<NotFound />);

      // Assert
      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass('items-center', 'justify-center');
      
      const textContainer = mainContainer.firstChild;
      expect(textContainer).toHaveClass('text-center');
    });
  });

  describe('Visual Design', () => {
    test('should use appropriate background color', () => {
      // Act
      const { container } = render(<NotFound />);

      // Assert
      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass('bg-gray-100');
    });

    test('should use appropriate text colors', () => {
      // Act
      render(<NotFound />);

      // Assert
      const description = screen.getByText('Oops! Page not found');
      expect(description).toHaveClass('text-gray-600');
      
      const homeLink = screen.getByText('Return to Home');
      expect(homeLink).toHaveClass('text-blue-500');
    });

    test('should include hover effects on link', () => {
      // Act
      render(<NotFound />);

      // Assert
      const homeLink = screen.getByText('Return to Home');
      expect(homeLink).toHaveClass('hover:text-blue-700');
    });
  });
});
