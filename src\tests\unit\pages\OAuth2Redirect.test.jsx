window.setItemCalls = [];
const originalLocalStorage = window.localStorage;
window.localStorage = {
  setItem: (...args) => window.setItemCalls.push(args),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

import React from 'react';
import { render, waitFor } from '@testing-library/react';
import OAuth2Redirect from '@/pages/OAuth2Redirect';
import { BrowserRouter } from 'react-router-dom';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('OAuth2Redirect', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    window.setItemCalls = [];
  });

  function renderWithRouter() {
    return render(
      <BrowserRouter>
        <OAuth2Redirect />
      </BrowserRouter>
    );
  }

  test('redirects to /signin if any token is missing', async () => {
    window.history.pushState({}, '', '/?sessionToken=abc&refreshToken=def');
    renderWithRouter();
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
    });
  });

  test('always renders spinner', () => {
    window.history.pushState({}, '', '/');
    const { getByRole } = renderWithRouter();
    expect(getByRole('progressbar')).toBeInTheDocument();
  });
}); 