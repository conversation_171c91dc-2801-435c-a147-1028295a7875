import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import Profile from '@/pages/Profile';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  ArrowLeft: () => <span data-testid="arrow-left-icon">←</span>,
  User: () => <span data-testid="user-icon">👤</span>,
  Mail: () => <span data-testid="mail-icon">📧</span>,
  Phone: () => <span data-testid="phone-icon">📞</span>,
  Calendar: () => <span data-testid="calendar-icon">📅</span>
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Box: ({ children }) => <div data-testid="mui-box">{children}</div>,
  Container: ({ children, maxWidth }) => (
    <div data-testid="container" data-max-width={maxWidth}>{children}</div>
  ),
  Paper: ({ children }) => <div data-testid="paper">{children}</div>,
  Typography: ({ children, variant }) => (
    <div data-testid={`typography-${variant}`}>{children}</div>
  ),
  TextField: ({ label, value, disabled }) => (
    <div data-testid="textfield">
      <label>{label}</label>
      <input
        data-testid={`input-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        value={value}
        disabled={disabled}
      />
    </div>
  ),
  Button: ({ children, variant }) => (
    <button data-testid="button" data-variant={variant}>{children}</button>
  ),
  Avatar: ({ children }) => <div data-testid="avatar">{children}</div>,
  Grid: ({ children, container, item }) => (
    <div data-testid="grid" data-container={container} data-item={item}>{children}</div>
  ),
  Card: ({ children }) => <div data-testid="card">{children}</div>,
  CardContent: ({ children }) => <div data-testid="card-content">{children}</div>,
  Divider: () => <hr data-testid="divider" />,
  AppBar: ({ children, position }) => (
    <div data-testid="app-bar" data-position={position}>{children}</div>
  ),
  Toolbar: ({ children }) => <div data-testid="toolbar">{children}</div>,
  IconButton: ({ children, onClick }) => (
    <button data-testid="icon-button" onClick={onClick}>{children}</button>
  )
}));

describe('Profile Basic Tests', () => {
  const renderComponent = () => {
    return render(
      <BrowserRouter>
        <Profile />
      </BrowserRouter>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('renders the main container', () => {
      renderComponent();
      expect(screen.getByTestId('container')).toBeInTheDocument();
    });

    it('renders app bar', () => {
      renderComponent();
      expect(screen.getByTestId('app-bar')).toBeInTheDocument();
    });

    it('renders toolbar', () => {
      renderComponent();
      expect(screen.getByTestId('toolbar')).toBeInTheDocument();
    });

    it('renders back navigation button', () => {
      renderComponent();
      expect(screen.getByTestId('icon-button')).toBeInTheDocument();
      expect(screen.getByTestId('arrow-left-icon')).toBeInTheDocument();
    });
  });

  describe('Page Content', () => {
    it('displays page title', () => {
      renderComponent();
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
    });

    it('displays user information', () => {
      renderComponent();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+1 (555) 123-4567')).toBeInTheDocument();
    });

    it('renders user avatar', () => {
      renderComponent();
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveTextContent('JD');
    });

    // it('displays join date', () => {
    //   renderComponent();
    //   expect(screen.getByText(/15\/1\/2024/)).toBeInTheDocument();
    // });
  });

  describe('Component Structure', () => {
    it('has proper layout structure', () => {
      renderComponent();

      expect(screen.getByTestId('app-bar')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toBeInTheDocument();
    });

    it('renders grid layout', () => {
      renderComponent();
      expect(screen.getAllByTestId('grid').length).toBeGreaterThan(0);
    });

    it('renders cards for user information', () => {
      renderComponent();
      expect(screen.getAllByTestId('card').length).toBeGreaterThan(0);
    });

    it('has dividers for content separation', () => {
      renderComponent();
      expect(screen.getAllByTestId('divider').length).toBeGreaterThan(0);
    });
  });

  describe('Icons and Visual Elements', () => {
    it('displays back arrow icon', () => {
      renderComponent();
      expect(screen.getByTestId('arrow-left-icon')).toBeInTheDocument();
    });

    it('renders avatar with user initials', () => {
      renderComponent();
      expect(screen.getByTestId('avatar')).toBeInTheDocument();
      expect(screen.getByText('JD')).toBeInTheDocument();
    });
  });

  describe('Typography Elements', () => {
    it('renders various typography variants', () => {
      renderComponent();

      // Check for different typography variants
      expect(screen.getAllByTestId('typography-h6').length).toBeGreaterThan(0);
      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
    });

    it('displays user name in heading', () => {
      renderComponent();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('displays contact information', () => {
      renderComponent();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+1 (555) 123-4567')).toBeInTheDocument();
    });
  });

  describe('Interactive Elements', () => {
    it('renders edit buttons', () => {
      renderComponent();
      const buttons = screen.getAllByTestId('button');
      expect(buttons.length).toBeGreaterThan(0);
    });

    it('has back navigation functionality', () => {
      renderComponent();
      const backButton = screen.getByTestId('icon-button');
      expect(backButton).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('uses container with max width', () => {
      renderComponent();
      const container = screen.getByTestId('container');
      expect(container).toHaveAttribute('data-max-width', 'md');
    });

    it('uses grid system for layout', () => {
      renderComponent();
      const grids = screen.getAllByTestId('grid');
      expect(grids.length).toBeGreaterThan(0);
    });
  });

  describe('User Data Display', () => {
    it('shows user avatar with initials', () => {
      renderComponent();
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toHaveTextContent('JD');
    });

    it('displays complete user profile information', () => {
      renderComponent();

      // Check for all user data fields
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+1 (555) 123-4567')).toBeInTheDocument();
      // expect(screen.getByText(/15\/1\/2024/)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      renderComponent();
      expect(screen.getAllByTestId('typography-h6')).toHaveLength(2);
      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
    });

    it('provides navigation controls', () => {
      renderComponent();
      expect(screen.getByTestId('icon-button')).toBeInTheDocument();
    });

    it('uses semantic elements', () => {
      renderComponent();
      expect(screen.getByTestId('app-bar')).toBeInTheDocument();
      expect(screen.getByTestId('toolbar')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('renders without console errors', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      renderComponent();
      
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('handles router context properly', () => {
      expect(() => renderComponent()).not.toThrow();
    });
  });

  describe('Integration', () => {
    it('integrates with Material-UI components', () => {
      renderComponent();

      expect(screen.getByTestId('app-bar')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getAllByTestId('card').length).toBeGreaterThan(0);
    });

    it('integrates with React Router', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('uses Lucide React icons', () => {
      renderComponent();

      expect(screen.getByTestId('arrow-left-icon')).toBeInTheDocument();
      // Only check for icons that are actually rendered in the component
    });
  });
});
