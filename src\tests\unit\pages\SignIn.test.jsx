import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import SignIn from '@/pages/SignIn';

// Mocks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const mockUseAuth = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

jest.mock('@mui/material/styles', () => ({
  ThemeProvider: ({ children }) => <div data-testid="theme-provider">{children}</div>,
  createTheme: () => ({}),
}));
jest.mock('@mui/material/CssBaseline', () => ({ __esModule: true, default: () => <div data-testid="css-baseline" /> }));
jest.mock('@/components/AuthContainer', () => ({
  __esModule: true,
  default: ({ initialView }) => <div data-testid="auth-container">AuthContainer: {initialView}</div>,
}));

describe('SignIn Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  function renderWithRouter(ui) {
    return render(<BrowserRouter>{ui}</BrowserRouter>);
  }

  it('renders AuthContainer with initialView="login" when not authenticated', () => {
    mockUseAuth.mockReturnValue({ user: null });
    renderWithRouter(<SignIn />);
    expect(screen.getByTestId('theme-provider')).toBeInTheDocument();
    expect(screen.getByTestId('css-baseline')).toBeInTheDocument();
    expect(screen.getByTestId('auth-container')).toHaveTextContent('AuthContainer: login');
  });

  it('redirects to /dashboard if user is authenticated', () => {
    mockUseAuth.mockReturnValue({ user: { id: '123', email: '<EMAIL>' } });
    renderWithRouter(<SignIn />);
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
  });

  it('does not render AuthContainer if user is authenticated', () => {
    mockUseAuth.mockReturnValue({ user: { id: '123', email: '<EMAIL>' } });
    renderWithRouter(<SignIn />);
    expect(screen.queryByTestId('auth-container')).not.toBeInTheDocument();
  });

  it('renders nothing if user is authenticated', () => {
    mockUseAuth.mockReturnValue({ user: { id: '123', email: '<EMAIL>' } });
    const { container } = renderWithRouter(<SignIn />);
    // Should only render empty fragment/null
    expect(container).toBeEmptyDOMElement();
  });
}); 