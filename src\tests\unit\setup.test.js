/**
 * Basic setup test to verify Jest configuration is working
 */

describe('Jest Setup', () => {
  it('should run basic tests', () => {
    expect(true).toBe(true);
  });

  it('should have access to Jest globals', () => {
    expect(jest).toBeDefined();
    expect(describe).toBeDefined();
    expect(it).toBeDefined();
    expect(expect).toBeDefined();
  });

  it('should have jsdom environment', () => {
    expect(window).toBeDefined();
    expect(document).toBeDefined();
    expect(document.createElement).toBeDefined();
  });

  it('should have testing library matchers', () => {
    const element = document.createElement('div');
    element.textContent = 'Hello World';
    document.body.appendChild(element);
    
    expect(element).toBeInTheDocument();
  });

  it('should mock localStorage', () => {
    expect(localStorage.setItem).toBeDefined();
    expect(localStorage.getItem).toBeDefined();
    expect(typeof localStorage.setItem).toBe('function');
  });

  it('should mock matchMedia', () => {
    expect(window.matchMedia).toBeDefined();
    expect(typeof window.matchMedia).toBe('function');
    
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    expect(mediaQuery).toHaveProperty('matches');
    expect(mediaQuery).toHaveProperty('media');
  });
});
